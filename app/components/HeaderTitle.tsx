import React, { FunctionComponent } from 'react';
import { TouchableOpacity, View } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { CustomText } from '../components/elements';
import { useTheme } from 'app/hooks/useTheme';
import { createThemedStyles } from 'app/utils/createThemedStyles';
import { Theme } from 'app/types/theme';

type HeaderTitleProps = {
  title: string;
  navigation: any;
  targetScreen?: any;
  convoPage?: string;
  profileScreen?: any;
  userDetails?: {

  };
};

// Container component (handles logic and theme)
const HeaderTitleContainer: FunctionComponent<HeaderTitleProps> = (props) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const handleNavigation = () => {
    const { navigation, targetScreen, convoPage, profileScreen, userDetails } = props;

    console.log('Target Screen:', targetScreen);
    console.log('Convo Page:', convoPage);

    if(convoPage){
      console.log('Navigating to convoPage:', convoPage);
      navigation.navigate(convoPage)
    } else if(targetScreen){
      navigation.navigate(targetScreen)
    }
    else if(profileScreen){
      navigation.navigate(profileScreen, {userDetails})
    }
     else{
      navigation.goBack();
    }
  };

  return (
    <HeaderTitlePresentation
      {...props}
      theme={theme}
      styles={styles}
      onNavigation={handleNavigation}
    />
  );
};

// Presentational component (pure UI rendering)
interface HeaderTitlePresentationProps extends HeaderTitleProps {
  theme: Theme;
  styles: any;
  onNavigation: () => void;
}

const HeaderTitlePresentation: FunctionComponent<HeaderTitlePresentationProps> = ({
  title,
  theme,
  styles,
  onNavigation,
}) => {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        onPress={onNavigation}
        style={styles.backButton}
        hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
      >
        <Ionicons
          name='chevron-back'
          size={22}
          color={theme.colors.headerText}
          style={styles.backIcon}
        />
      </TouchableOpacity>
        <CustomText style={styles.titleText}>
          {title}
        </CustomText>
    </View>
  );
};

// Styled component creation
const createStyles = createThemedStyles((theme: Theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: rv(5),
    paddingVertical: rv(5),
    backgroundColor: theme.colors.headerBackground,
    minHeight: rv(40),
  },
  backButton: {
    marginLeft: rv(10),
    marginRight: rv(15),
    padding: rv(8),
    borderRadius: rv(20),
    backgroundColor: 'transparent',
  },
  backIcon: {
    // No additional margin needed
  },
  titleText: {
    fontSize: rv(12),
    fontFamily: 'medium',
    color: theme.colors.headerText,
    flex: 1,
    textAlign: 'center',
    textAlignVertical: 'center',
    includeFontPadding: false,
  },
}));

// Export the container component as default
const HeaderTitle = HeaderTitleContainer;

export default HeaderTitle;
