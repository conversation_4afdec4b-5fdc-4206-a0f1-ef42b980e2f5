import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useTheme } from 'app/hooks/useTheme';
import { createThemedStyles } from 'app/utils/createThemedStyles';
import { responsiveValue as rv } from 'app/providers/responsive-value';

/**
 * Example component demonstrating proper theme usage
 * This shows the pattern that should be followed in all components
 */

// Container component (handles logic and theme)
export const ThemedComponentExampleContainer: React.FC = () => {
  const { theme, toggleTheme, themeMode } = useTheme();
  const styles = createStyles(theme);

  const handleThemeToggle = () => {
    toggleTheme();
  };

  return (
    <ThemedComponentExamplePresentation
      theme={theme}
      styles={styles}
      themeMode={themeMode}
      onThemeToggle={handleThemeToggle}
    />
  );
};

// Presentational component (pure UI rendering)
interface ThemedComponentExamplePresentationProps {
  theme: any;
  styles: any;
  themeMode: string;
  onThemeToggle: () => void;
}

const ThemedComponentExamplePresentation: React.FC<ThemedComponentExamplePresentationProps> = ({
  theme,
  styles,
  themeMode,
  onThemeToggle,
}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Theme Example</Text>
      <Text style={styles.subtitle}>Current mode: {themeMode}</Text>
      
      <View style={styles.colorPalette}>
        <View style={[styles.colorBox, { backgroundColor: theme.colors.primary }]} />
        <View style={[styles.colorBox, { backgroundColor: theme.colors.secondary }]} />
        <View style={[styles.colorBox, { backgroundColor: theme.colors.accent }]} />
      </View>
      
      <TouchableOpacity style={styles.button} onPress={onThemeToggle}>
        <Text style={styles.buttonText}>Toggle Theme</Text>
      </TouchableOpacity>
    </View>
  );
};

// Themed styles using the createThemedStyles utility
const createStyles = createThemedStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: rv(24),
    fontFamily: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  subtitle: {
    fontSize: rv(16),
    fontFamily: 'medium',
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.lg,
  },
  colorPalette: {
    flexDirection: 'row',
    marginBottom: theme.spacing.lg,
  },
  colorBox: {
    width: rv(50),
    height: rv(50),
    marginHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  },
  button: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    ...theme.shadows.medium,
  },
  buttonText: {
    color: theme.colors.background,
    fontSize: rv(16),
    fontFamily: 'medium',
  },
}));

// Export the container component as default
export default ThemedComponentExampleContainer;
