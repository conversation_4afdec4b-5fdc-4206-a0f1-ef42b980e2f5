import React, { FunctionComponent, useMemo } from 'react';
import { Text, StyleSheet, TextStyle, TextProps } from 'react-native';
import { responsiveValue as rv, responsiveFontSize } from 'app/providers/responsive-value';
import Hyperlink from 'react-native-hyperlink';
import { Linking } from 'react-native';
import { useTheme, createThemedStyles } from 'app/theme';
import { useFontSize } from 'app/hooks/useFontSize';

interface CustomTextProps extends TextProps {
  style?: TextStyle | TextStyle[];
  textType?: 'light' | 'regular' | 'bold' | 'semi-bold' | 'extra-bold' | 'medium';
  children: any;
  onPress?: () => void
}

// Presentation component props
interface CustomTextPresentationProps extends CustomTextProps {
  theme: any;
  styles: any;
  fontSizeMultiplier: number;
}

// Create themed styles with font size scaling
const createStyles = (theme: any, fontSizeMultiplier: number) => StyleSheet.create({
  regular: {
    fontFamily: 'regular',
    fontSize: responsiveFontSize(14, fontSizeMultiplier),
    color: theme.colors.text
  },
  medium: {
    fontFamily: 'medium',
    fontSize: responsiveFontSize(14, fontSizeMultiplier),
    color: theme.colors.text
  },
  bold: {
    fontFamily: 'bold',
    fontSize: responsiveFontSize(14, fontSizeMultiplier),
    color: theme.colors.text
  },
  extraBold: {
    fontFamily: 'italic',
    fontSize: responsiveFontSize(14, fontSizeMultiplier),
    color: theme.colors.text
  },
  semiBold: {
    fontFamily: 'semiBold',
    fontSize: responsiveFontSize(14, fontSizeMultiplier),
    color: theme.colors.text
  },
  black: {
    fontFamily: 'black',
    fontSize: responsiveFontSize(14, fontSizeMultiplier),
    color: theme.colors.text
  },
  italic: {
    fontFamily: 'italic',
    fontSize: responsiveFontSize(14, fontSizeMultiplier),
    color: theme.colors.text
  },
  light: {
    fontFamily: 'regular',
    fontSize: responsiveFontSize(14, fontSizeMultiplier),
    color: theme.colors.text
  }
});

// Container Component (handles logic, state, theme)
const CustomTextContainer: FunctionComponent<CustomTextProps> = (props) => {
  const { theme } = useTheme();
  const { fontSizeMultiplier } = useFontSize();
  const styles = createStyles(theme, fontSizeMultiplier);

  return (
    <CustomTextPresentation
      theme={theme}
      styles={styles}
      fontSizeMultiplier={fontSizeMultiplier}
      {...props}
    />
  );
};

// Presentational Component (pure UI)
const CustomTextPresentation: FunctionComponent<CustomTextPresentationProps> = ({
  theme,
  styles,
  children,
  textType,
  style,
  fontSizeMultiplier,
  ...rest
}) => {
  const textStyle = useMemo(() => {
    switch (textType) {
      case 'medium':
        return styles.medium;
      case 'regular':
        return styles.regular;
      case 'bold':
        return styles.bold;
      case 'extra-bold':
        return styles.extraBold;
      case 'semi-bold':
        return styles.semiBold;
      case 'light':
        return styles.light;
      default:
        return styles.regular;
    }
  }, [textType, styles]);

  const passedStyles = useMemo(() => {
    const flattenedStyle = StyleSheet.flatten(style || {});

    // If the passed style has a fontSize, scale it with the font size multiplier
    if (flattenedStyle.fontSize) {
      return {
        ...flattenedStyle,
        fontSize: responsiveFontSize(flattenedStyle.fontSize, fontSizeMultiplier)
      };
    }

    return flattenedStyle;
  }, [style, fontSizeMultiplier]);

  return (
    <Hyperlink
      linkStyle={{
        color: theme.colors.accent,
        textDecorationLine: 'underline',
        fontSize: responsiveFontSize(14, fontSizeMultiplier)
      }}
      onPress={(url) => Linking.openURL(url)}
    >
      <Text style={[textStyle, passedStyles]} {...rest}>
        {children}
      </Text>
    </Hyperlink>
  );
};

// Export container as default
export const CustomText = CustomTextContainer;


