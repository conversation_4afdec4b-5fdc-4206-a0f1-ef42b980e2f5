import React, { FunctionComponent, useMemo } from 'react';
import { Text, StyleSheet, TextStyle, TextProps } from 'react-native';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import Hyperlink from 'react-native-hyperlink';
import { Linking } from 'react-native';
import { useTheme, createThemedStyles } from 'app/theme';

interface CustomTextProps extends TextProps {
  style?: TextStyle | TextStyle[];
  textType?: 'light' | 'regular' | 'bold' | 'semi-bold' | 'extra-bold' | 'medium';
  children: any;
  onPress?: () => void
}

// Presentation component props
interface CustomTextPresentationProps extends CustomTextProps {
  theme: any;
  styles: any;
}

// Create themed styles
const createStyles = createThemedStyles((theme) => ({
  regular: {
    fontFamily: 'regular',
    color: theme.colors.text
  },
  medium: {
    fontFamily: 'medium',
    color: theme.colors.text
  },
  bold: {
    fontFamily: 'bold',
    fontSize: rv(13),
    color: theme.colors.text
  },
  extraBold: {
    fontFamily: 'italic',
    color: theme.colors.text
  },
  semiBold: {
    fontFamily: 'semiBold',
    color: theme.colors.text
  },
  black: {
    fontFamily: 'black',
    color: theme.colors.text
  },
  italic: {
    fontFamily: 'italic',
    color: theme.colors.text
  },
  light: {
    fontFamily: 'regular',
    color: theme.colors.text
  }
}));

// Container Component (handles logic, state, theme)
const CustomTextContainer: FunctionComponent<CustomTextProps> = (props) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);

  return (
    <CustomTextPresentation
      theme={theme}
      styles={styles}
      {...props}
    />
  );
};

// Presentational Component (pure UI)
const CustomTextPresentation: FunctionComponent<CustomTextPresentationProps> = ({
  theme,
  styles,
  children,
  textType,
  style,
  ...rest
}) => {
  const textStyle = useMemo(() => {
    switch (textType) {
      case 'medium':
        return styles.medium;
      case 'regular':
        return styles.regular;
      case 'bold':
        return styles.bold;
      case 'extra-bold':
        return styles.extraBold;
      case 'semi-bold':
        return styles.semiBold;
      case 'light':
        return styles.light;
      default:
        return styles.regular;
    }
  }, [textType, styles]);

  const passedStyles = useMemo(() => StyleSheet.flatten(style || {}), [style]);

  return (
    <Hyperlink
      linkStyle={{ color: theme.colors.accent, textDecorationLine: 'underline' }}
      onPress={(url) => Linking.openURL(url)}
    >
      <Text style={[textStyle, passedStyles]} {...rest}>
        {children}
      </Text>
    </Hyperlink>
  );
};

// Export container as default
export const CustomText = CustomTextContainer;


