import React, { FunctionComponent } from 'react';
import {
  TextStyle,
  Image,
} from 'react-native';
import { CustomText } from './Text';
import Ionicons from '@expo/vector-icons/Ionicons';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import TrackTouchable from '../TrackTouchable';
import { useTheme, createThemedStyles } from 'app/theme';

type CustomButtonProps = {
  style?: TextStyle | TextStyle[];
  buttonTheme?: 'primary' | 'secondary' | 'tertiary' | 'quaternary' | 'quinary';
  label: string;
  icon?: keyof typeof Ionicons.glyphMap;
  onPress?: () => void;
  loading?: boolean;
  disabled?: boolean;
  textSize?: number;
};

// Presentation component props
interface CustomButtonPresentationProps extends CustomButtonProps {
  theme: any;
  styles: any;
  handlePress: () => void;
}

// Create themed styles
const createStyles = createThemedStyles((theme) => ({
  primary: {
    backgroundColor: theme.colors.buttonPrimary,
    width: '100%',
    alignSelf: 'center',
    height: rv(50)
  },
  secondary: {
    backgroundColor: theme.colors.buttonSecondary,
    paddingVertical: rv(12),
    alignSelf: 'center',
  },
  tertiary: {
    backgroundColor: theme.colors.buttonTertiary,
    borderWidth: 0.5,
    marginTop: rv(20),
    width: '100%',
    borderColor: theme.colors.primary,
  },
  quaternary: {
    backgroundColor: theme.colors.buttonQuaternary,
    width: '100%',
    alignSelf: 'center',
  },
  quinary: {
    backgroundColor: theme.colors.buttonQuinary,
    width: '100%',
  },
  textPrimary: {
    color: theme.colors.background,
    fontFamily: 'medium'
  },
  textSecondary: {
    color: theme.colors.primary,
  },
  textTertiary: {
    color: theme.colors.text,
    fontFamily: 'bold',
    textAlign: 'center',
    fontSize: rv(14)
  },
  textQuaternary: {
    color: theme.colors.background,
    fontSize: rv(13),
    fontFamily: 'medium'
  },
  textQuinary: {
    color: theme.colors.background,
    fontFamily: 'bold'
  }
}));

// Container Component (handles logic, state, theme)
const CustomButtonContainer: FunctionComponent<CustomButtonProps> = (props) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const handlePress = () => {
    if (!props.loading && !props.disabled && props.onPress) {
      props.onPress();
    }
  };

  return (
    <CustomButtonPresentation
      theme={theme}
      styles={styles}
      handlePress={handlePress}
      {...props}
    />
  );
};

// Presentational Component (pure UI)
const CustomButtonPresentation: FunctionComponent<CustomButtonPresentationProps> = ({
  theme,
  styles,
  handlePress,
  buttonTheme = 'primary',
  style,
  label,
  icon,
  loading = false,
  disabled = false,
  textSize = rv(14),
}) => {
  let buttonStyle: {};
  let textStyle = {};

  switch (buttonTheme) {
    case 'primary':
      buttonStyle = loading ? styles.secondary : styles.primary;
      textStyle = styles.textPrimary;
      break;
    case 'secondary':
      buttonStyle = styles.secondary;
      textStyle = styles.textSecondary;
      break;
    case 'tertiary':
      buttonStyle = styles.tertiary;
      textStyle = styles.textTertiary;
      break;
    case 'quaternary':
      buttonStyle = styles.quaternary;
      textStyle = styles.textQuaternary;
      break;
    case 'quinary':
      buttonStyle = styles.quinary;
      textStyle = styles.textQuinary;
      break;
    default:
      buttonStyle = loading ? styles.secondary : styles.primary;
      textStyle = styles.textPrimary;
      break;
  }

  const passedStyles = Array.isArray(style)
    ? Object.assign({}, ...style)
    : style;

  // Get text color based on button theme
  const getTextColor = () => {
    switch (buttonTheme) {
      case 'secondary':
        return theme.colors.primary;
      case 'primary':
      case 'quaternary':
      case 'quinary':
        return theme.colors.background;
      default:
        return theme.colors.text;
    }
  };

  // Get icon color based on button theme
  const getIconColor = () => {
    switch (buttonTheme) {
      case 'secondary':
        return theme.colors.primary;
      default:
        return theme.colors.text;
    }
  };

  return (
    <TrackTouchable
      onPress={handlePress}
      style={{
        ...buttonStyle,
        width: '100%',
        height: rv(50),
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        opacity: loading || disabled ? 0.5 : 1,
        ...passedStyles,
      }}
      disabled={loading || disabled}
    >
      {loading ? (
        <Image
          style={{
            width: 30,
            height: 30,
            alignSelf: 'center',
          }}
          source={require('app/assets/loading.gif')}
        />
      ) : (
        <>
          <CustomText
            style={{
              ...textStyle,
              textAlign: 'center',
              fontSize: textSize,
              marginTop: 3,
              fontFamily: 'medium',
              color: getTextColor(),
            }}
          >
            {label}
          </CustomText>

          {icon ? (
            <Ionicons
              name={icon}
              style={{
                marginLeft: 6,
              }}
              size={22}
              color={getIconColor()}
            />
          ) : null}
        </>
      )}
    </TrackTouchable>
  );
};

// Export container as default
export const CustomButton = CustomButtonContainer;

