import React, { FunctionComponent, useEffect, useState } from 'react';
import { TouchableOpacity, View, Text, FlatList } from 'react-native';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { Axios } from 'app/api/axios';
import CustomCheckbox from './CustomCheckBox';
import Ionicons from '@expo/vector-icons/Ionicons';
import { CustomText } from './Text';
import { useTheme } from 'app/hooks/useTheme';
import { createOptimizedThemedStyles, createThemedStyles } from 'app/utils/createThemedStyles';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';

type SelectInterestsProps =
  | {
      multiple: boolean;
      setInterests: (interests: string[]) => void;
      interests: string[];
      interest?: string;
      setInterest?: any;
    }
  | {
      multiple?: never;
      setInterests?: any;
      interests?: string[];
      interest: string;
      setInterest: (interest: string) => void;
    };

interface IInterestButtonProps {
  item: IInterest;
  onDelete: () => void;
  theme: any;
  styles: any;
}

const InterestButton: FunctionComponent<IInterestButtonProps> = ({
  item,
  onDelete,
  theme,
  styles,
}) => {
  return (
    <View style={{ flexDirection: 'row', alignItems: 'center', margin: rv(3) }}>
      <TouchableOpacity
        onPress={onDelete}
        style={styles.interestButton}
      >
        <CustomText
          style={styles.interestButtonText}
        >
          {item.name}
        </CustomText>
        <CustomText style={styles.interestButtonCloseText}>X</CustomText>
      </TouchableOpacity>
    </View>
  );
};

const SelectInterests: FunctionComponent<SelectInterestsProps> = ({
  setInterests,
  interests = [],
  setInterest,
  interest,
  multiple,
}) => {
  const [allInterests, setAllInterests] = useState<IInterest[]>([]);
  const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);
   const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  useEffect(() => {
    Axios({
      method: 'get',
      url: '/tags/list',
    }).then((response: any) => {
      let interestData = JSON.parse(JSON.stringify(response.data.data));
      interestData.sort((a: IInterest, b: IInterest) => {
        if (a.name.toLowerCase() === 'business') return -1;
        if (b.name.toLowerCase() === 'business') return 1;
        return 0;
      });
      setAllInterests(interestData);
    });
  }, []);

  const handleSelectInterest = (value: string) => {
    if (multiple) {
      const newSelectedItems = interests.includes(value)
        ? interests.filter((item) => item !== value)
        : [...interests, value];
      setInterests(newSelectedItems);
    } else {
      setInterest(value);
      setDropdownVisible(false);
    }
  };

  const handleRemoveInterest = (id: string) => {
    if (multiple) {
      setInterests(interests.filter((item) => item !== id));
    } else {
      setInterest('');
    }
  };

  return (
    <View style={styles.container}>
      {/* Render Selected Interests */}
      <View>
        <View style={styles.selectedInterestsContainer}>
          {multiple
            ? interests.map((id) => {
                const item = allInterests.find(
                  (interest) => interest._id === id
                );
                return item ? (
                  <InterestButton
                    key={item._id}
                    item={item}
                    onDelete={() => handleRemoveInterest(item._id)}
                    theme={theme}
                    styles={styles}
                  />
                ) : null;
              })
            : interest &&
              allInterests.find((item) => item._id === interest) && (
                <InterestButton
                  key={interest}
                  item={allInterests.find((item) => item._id === interest)!}
                  onDelete={() => handleRemoveInterest(interest)}
                  theme={theme}
                  styles={styles}
                />
              )}
        </View>
      </View>

      {/* Dropdown button for selecting an interest */}
      <TouchableOpacity
        onPress={() => setDropdownVisible(!dropdownVisible)}
        style={styles.dropdownButton}
      >
        <CustomText style={styles.dropdownButtonText}>
          {interest
            ? allInterests.find((item) => item._id === interest)?.name
            : 'Click to select your interests'}
        </CustomText>
        <Ionicons
          name={'chevron-down'}
          size={rv(14)}
          color={theme.colors.text}
        />
      </TouchableOpacity>

      {/* Dropdown List */}
      {dropdownVisible && (
        <View style={styles.dropdownContainer}>
          <FlatList
            data={allInterests}
            keyExtractor={(item) => item._id}
            nestedScrollEnabled
            renderItem={({ item }) => (
              <TouchableOpacity
                onPress={() => handleSelectInterest(item._id)}
                style={styles.dropdownItem}
              >
                <CustomCheckbox
                  value={
                    multiple
                      ? interests.includes(item._id)
                      : interest === item._id
                  }
                  onValueChange={() => handleSelectInterest(item._id)}
                  color={theme.colors.primary}
                  size={24}
                />
                <CustomText style={styles.dropdownItemText} textType='medium'>
                  {item.name}
                </CustomText>
              </TouchableOpacity>
            )}
            style={styles.flatList}
          />
        </View>
      )}
    </View>
  );
};

// Themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    padding: rv(10),
  },
  selectedInterestsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: rv(5),
  },
  interestButton: {
    backgroundColor: 'transparent',
    borderRadius: rv(32),
    padding: rv(10),
    paddingHorizontal: rv(20),
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  interestButtonText: {
    color: theme.colors.text,
    fontSize: fs("XS"),
    marginRight: rv(10),
    fontFamily: 'medium',
  },
  interestButtonCloseText: {
    color: theme.colors.text,
    fontSize: fs("XS"),
  },
  dropdownButton: {
    borderColor: theme.colors.border,
    borderRadius: 6,
    borderWidth: 1,
    padding: rv(10),
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.inputBackground || theme.colors.surface,
  },
  dropdownButtonText: {
    fontSize: fs("BASE"),
    flex: 1,
    fontFamily: 'medium',
    color: theme.colors.text,
    lineHeight:fs("MD"),
  },
  dropdownContainer: {
    borderRadius: rv(6),
    borderColor: theme.colors.border,
    borderWidth: 1,
    marginTop: rv(10),
    maxHeight: rv(200),
    backgroundColor: theme.colors.surface,
  },
  dropdownItem: {
    padding: rv(10),
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
  },
  dropdownItemText: {
    fontSize: fs("MD"),
    color: theme.colors.textSecondary,
    fontFamily: 'medium',
    marginLeft: rv(10),
  },
  flatList: {
    maxHeight: rv(200),
  },
}));

export default SelectInterests;
