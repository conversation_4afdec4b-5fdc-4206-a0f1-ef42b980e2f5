import React from 'react';
import { TouchableOpacity, Text } from 'react-native';
import { useTheme } from 'app/hooks/useTheme';
import { createThemedStyles } from 'app/utils/createThemedStyles';
import { responsiveValue as rv } from 'app/providers/responsive-value';

/**
 * Simple theme toggle button for testing and development
 * Can be temporarily added to any screen to test theme switching
 */
export const ThemeToggleButton: React.FC = () => {
  const { theme, themeMode, toggleTheme } = useTheme();
  const styles = createStyles(theme);

  const getButtonText = () => {
    switch (themeMode) {
      case 'light':
        return '🌙 Dark';
      case 'dark':
        return '☀️ Light';
      case 'system':
        return '🔄 System';
      default:
        return '🌙 Dark';
    }
  };

  return (
    <TouchableOpacity style={styles.button} onPress={toggleTheme}>
      <Text style={styles.text}>
        {getButtonText()}
      </Text>
    </TouchableOpacity>
  );
};

const createStyles = createThemedStyles((theme) => ({
  button: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    alignSelf: 'center' as const,
    margin: theme.spacing.md,
    ...theme.shadows.small,
  },
  text: {
    color: theme.colors.background,
    fontSize: rv(14),
    fontFamily: 'medium' as const,
    textAlign: 'center' as const,
  },
}));

export default ThemeToggleButton;
