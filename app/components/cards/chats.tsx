import { View, TouchableOpacity, StyleSheet } from 'react-native';
import React, { FunctionComponent, useEffect, useState } from 'react';
import Avatar from 'app/components/elements/Avater';
import { CustomText } from 'app/components/elements/Text';
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
  renderers,
} from 'react-native-popup-menu';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { useSelector } from 'react-redux';
import { userId } from 'app/redux/user/reducer';
import ReportDialog from 'app/components/dialogs/report';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Axios } from 'app/api/axios';
import { ShowAlert } from 'app/providers/toast';
import { timeAgo } from 'app/helpers/time-ago';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import MenuSVG from 'app/assets/svg/menu.svg';
import { useTranslation } from 'react-i18next';
import MuteSVG from 'app/assets/svg/muteUser.svg';
import ReportGroupSVG from 'app/assets/svg/reportUser.svg';
import UnMuteSVG from 'app/assets/svg/unmuteUser.svg';
import DarkBlockedChatSVG from 'app/assets/svg/Block_chats.svg';

import { showModal, hideModal } from 'app/providers/modals';
import FlagSVG from 'app/assets/svg/flagasInapp.svg';
// import { DarkBlockedChatSVG } from 'app/providers/svg/loader';
import { useGetUnreadNotifications } from 'app/redux/main/hooks';
import ConvoNotificationIcon from '../convo-icon';
import { main } from 'app/api/main';
import { useTheme } from 'app/hooks/useTheme';
import { createThemedStyles } from 'app/utils/createThemedStyles';

interface ChatProps {
  item: any;
  navigation: any;
  onToggleMuted?: () => {};
  refreshFunction?: () => {};
  route: any;
  count: number;
}

// Container Component
const ChatCardContainer: FunctionComponent<ChatProps> = ({
  item,
  route,
  navigation,
  refreshFunction,
  onToggleMuted,
}) => {
  const { SlideInMenu } = renderers;
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const myId = useSelector(userId);

  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportDetails, setReportDetails] = useState({});
  const { t } = useTranslation();
  const [isMuted, setIsMuted] = useState(item.muted);

  function openReportDetails(item: any) {
    setReportDetails({ data: { _id: item }, type: 'user' });
    setShowReportDialog(true);
  }
  console.log(item, 'item');


  async function removeFromList(accountId: string) {
    await Axios({
      method: 'patch',
      url: '/chats/remove-from-chat/' + accountId,
      data: {},
    }).then((response: any) => {
      showModal({
        modalVisible: true,
        title: 'Success',
        message: response.data.message,
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
        navigation,
      });

      // ShowAlert({
      //   type: 'success',
      //   className: 'Success',
      //   message: response.data.message,
      // });
      refreshFunction!();
    });
  }

  async function blockUser(accountId: string) {
    await Axios({
      method: 'patch',
      url: '/chats/block/' + accountId,
      data: {},
    }).then((response: any) => {
      const message = response.data.message === 'Blocked successfully' ? 'User blocked' : response.data.message

      showModal({
        modalVisible: true,
        title: 'Success',
        message: message,
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'success',
      //   className: 'Success',
      //   message: response.data.message,
      // });
      refreshFunction!();
    });
  }

  async function unBlockUser(accountId: string) {
    await Axios({
      method: 'patch',
      url: '/chats/unblock/' + accountId,
      data: {},
    }).then((response: any) => {
      const message = response.data.message === 'Chat has been unblocked successfully' ? 'User unblocked' : response.data.message

      showModal({
        modalVisible: true,
        title: 'Success',
        message: message,
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'success',
      //   className: 'Success',
      //   message: response.data.message,
      // });
      refreshFunction!();
    });
  }

  function muteNotification(item: {
    _id: string;
    muted?: boolean;
    accountId: AccountProfileProp;
  })

  {
       const message = item.muted ? 'Chat unmuted': 'This chat has now been muted'

    Axios({
      method: 'post',
      url: '/app/mute-notifications/',
      data: {
        contentId: item.accountId._id,
        type: 'user',
        action: item.muted ? 'unmute' : 'mute',
      },
    }).then((response: any) => {
      showModal({
        modalVisible: true,
        title: 'Success',
        message: message,
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'success',
      //   className: 'Success',
      //   message: 'User has been muted successfully',
      // });
      refreshFunction!();
    });
  }

  const handleToggleMuted = () => {
    setIsMuted((prevMuted: any) => !prevMuted);
    if (onToggleMuted) {
      onToggleMuted();
    }
  };

  useEffect(() => {
    console.log('item', item);
  }, [item]);

  return (
    <ChatCardPresentation
      theme={theme}
      styles={styles}
      item={item}
      navigation={navigation}
      t={t}
      myId={myId}
      isMuted={isMuted}
      showReportDialog={showReportDialog}
      reportDetails={reportDetails}
      removeFromList={removeFromList}
      blockUser={blockUser}
      unBlockUser={unBlockUser}
      muteNotification={muteNotification}
      openReportDetails={openReportDetails}
      setShowReportDialog={setShowReportDialog}
      SlideInMenu={SlideInMenu}
    />
  );
};

// Presentation Component
interface ChatCardPresentationProps {
  theme: any;
  styles: any;
  item: any;
  navigation: any;
  t: any;
  myId: string;
  isMuted: boolean;
  showReportDialog: boolean;
  reportDetails: any;
  removeFromList: (accountId: string) => void;
  blockUser: (accountId: string) => void;
  unBlockUser: (accountId: string) => void;
  muteNotification: (item: any) => void;
  openReportDetails: (item: any) => void;
  setShowReportDialog: (show: boolean) => void;
  SlideInMenu: any;
}

const ChatCardPresentation: React.FC<ChatCardPresentationProps> = ({
  theme,
  styles,
  item,
  navigation,
  t,
  myId,
  isMuted,
  showReportDialog,
  reportDetails,
  removeFromList,
  blockUser,
  unBlockUser,
  muteNotification,
  openReportDetails,
  setShowReportDialog,
  SlideInMenu,
}) => {
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() =>
        navigation.push('Common', {
          screen: 'private-chat',
          params: {
            userDetails: item.accountId,
          },
        })
      }
    >
      <ReportDialog
        show={showReportDialog}
        setShow={setShowReportDialog}
        reportDetails={reportDetails}
      />
      <View>
        <View style={styles.avatarContainer}>
          <Avatar source={item?.accountId?.profile_picture} size={rv(45)} />
        </View>
      </View>
      <View style={styles.contentContainer}>
        <View style={styles.headerRow}>
          <View style={styles.nameSection}>
            <TouchableOpacity
              onPress={() =>
                navigation.navigate('Common', {
                  screen: 'private-chat',
                  params: {
                    userDetails: item.accountId,
                  },
                })
              }
            >
              {item &&
              item.accountId &&
              item.accountId.first_name + item.accountId.last_name.length >
                12 ? (
                <CustomText style={styles.userName}>
                  {item.accountId.first_name +
                    ' ' +
                    item.accountId.last_name.slice(0, 12)}
                  ...
                </CustomText>
              ) : (
                <CustomText style={styles.userName}>
                  {item.accountId
                    ? item.accountId.first_name + ' ' + item.accountId.last_name
                    : null}
                </CustomText>
              )}
            </TouchableOpacity>
          </View>
          <Menu
            renderer={SlideInMenu}
            // onSelect={(value) => {
            //   //onMenuClicked(value)
            // }}
          >
            <MenuTrigger
            // customStyles={{
            // triggerTouchable: {
            //   padding: 100, // Add padding to increase the clickable area
            //   alignItems: 'center', // Center the content
            //   justifyContent: 'center',
            // }}}
            >
              <View
                style={{
                  alignItems: 'flex-end',
                  flexDirection: 'row',
                  paddingRight: rv(7),
                }}
              >
                <MenuSVG width={15} height={15} />
              </View>
            </MenuTrigger>

            <MenuOptions
              customStyles={{
                optionText: [styles.menuText],
                optionsContainer: [styles.menuOptionsContainer],
              }}
            >
              <View style={styles.menuContent}>
                {/* <MenuOption
                  onSelect={() => {
                    removeFromList(item.accountId._id);
                  }}
                >
                  <View
                    style={{
                      flexDirection: 'row',
                      width: '100%',
                      marginBottom: 10,
                      alignItems: 'center',
                    }}
                  >
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '100%',

                        alignItems: 'center',
                      }}
                    >
                      <FlagSVG width={20} />
                      <CustomText
                        style={{
                         color: 'black',
                          fontSize: rv(12),
                          marginLeft: rv(3),
                          fontFamily: 'medium',
                        }}
                      >
                        {t('convos_SubMenu')}
                      </CustomText>
                    </View>
                  </View>
                </MenuOption> */}

                <MenuOption
                  onSelect={() => {
                    if (item.blocked && item.blocked.includes(myId)) {
                      unBlockUser(item.accountId._id);
                    } else {
                      blockUser(item.accountId._id);
                    }
                  }}
                >
                  <View
                    style={{
                      flexDirection: 'row',
                      width: '100%',
                      alignItems: 'center',
                      marginBottom: 10,
                    }}
                  >
                    <View
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}
                    >
                      {item.blocked ? (
                        <DarkBlockedChatSVG width={18} />
                      ) : (
                        <DarkBlockedChatSVG width={18} />
                      )}
                    </View>
                    <CustomText style={styles.menuItemText}>
                      {item.blocked && item.blocked.includes(myId)
                        ? t('BlockedChats_opt1')
                        : t('Chats_blockUser')}
                    </CustomText>
                  </View>
                </MenuOption>

                <MenuOption
                  onSelect={() => {
                    muteNotification(item);
                  }}
                >
                  <View
                    style={{
                      flexDirection: 'row',
                      width: '100%',
                      alignItems: 'center',
                      marginBottom: 10,
                    }}
                  >
                    <View
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}
                    >
                      {item.muted ? (
                        <MuteSVG width={20} />
                      ) : (
                        <UnMuteSVG width={20} />
                      )}
                    </View>
                    <CustomText style={styles.menuItemText}>
                      {item.muted ? 'Unmute User' : 'Mute User'}
                    </CustomText>
                  </View>
                </MenuOption>

                <MenuOption
                  onSelect={() => {
                    openReportDetails(item.accountId._id);
                  }}
                >
                  <View
                    style={{
                      flexDirection: 'row',
                      width: '100%',
                      alignItems: 'center',
                      // marginHorizontal: 10,
                      gap: rv(5),
                    }}
                  >
                    <ReportGroupSVG width={20} />
                    <CustomText style={styles.menuItemText}>
                      {t('Chats_reportUser')}
                    </CustomText>
                  </View>
                </MenuOption>
              </View>
            </MenuOptions>
          </Menu>
        </View>
        <View style={styles.userInfoContainer}>
          {item.accountId ? (
            <CustomText
              numberOfLines={2}
              style={styles.userPosition}
            >
              {item.accountId?.company_position && item.accountId?.company
                ? `${
                    item.accountId?.company_position?.replace(
                      /(\r\n|\n|\r)/gm,
                      ''
                    ) || 'Position'
                  } at ${item.accountId?.company}`
                : 'Position at Company'}
            </CustomText>
          ) : null}
        </View>
        <View style={styles.timeAndBadgeContainer}>
          {item.lastMessageDate ? (
            <CustomText style={styles.timeText}>
              {timeAgo(item.lastMessageDate)}
            </CustomText>
          ) : null}
          <View>
            {item.unreadMessages ? (
              <View style={styles.unreadBadge}>
                <CustomText style={styles.unreadBadgeText}>
                  {item.unreadMessages}
                </CustomText>
              </View>
            ) : null}
          </View>
        </View>

        <View style={styles.messageContainer}>
          <View style={styles.messageWrapper}>
            {item?.lastMessage ? (
              <CustomText
                numberOfLines={2}
                style={styles.messageText}
              >
                {item.lastMessage}
              </CustomText>
            ) : item.type ? (
              <CustomText
                ellipsizeMode='tail'
                numberOfLines={2}
                style={styles.messageText}
              >
                File shared with you
              </CustomText>
            ) : null}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

// Themed styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    flexDirection: 'row' as const,
    justifyContent: 'flex-start' as const,
    alignItems: 'flex-start' as const,
    backgroundColor: theme.colors.cardBackground,
    paddingVertical: rv(8),
    paddingLeft: rv(20),
  },
  avatarContainer: {
    marginBottom: 10,
    marginLeft: 5,
    marginRight: 15,
  },
  contentContainer: {
    width: '80%',
  },
  headerRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginRight: wp('5%'),
  },
  nameSection: {
    width: '60%',
  },
  userName: {
    color: theme.colors.text,
    fontSize: rv(13),
    fontFamily: 'bold' as const,
  },
  menuOptionsContainer: {
    borderRadius: 15,
    backgroundColor: theme.colors.surface,
  },
  menuContent: {
    margin: 5,
    flexDirection: 'column' as const,
    marginVertical: 10,
    padding: 15,
  },
  menuItemText: {
    fontSize: rv(12),
    marginLeft: rv(5),
    fontFamily: 'medium' as const,
    color: theme.colors.text,
  },
  menuText: {
    color: theme.colors.textSecondary,
    textAlign: 'center' as const,
    fontSize: rv(13),
    width: '87%',
    fontFamily: 'medium' as const,
  },
  userInfoContainer: {
    // Empty container style
  },
  userPosition: {
    fontSize: rv(12),
    color: theme.colors.textSecondary,
    flexWrap: 'nowrap' as const,
    width: '80%',
    fontFamily: 'medium' as const,
  },
  timeAndBadgeContainer: {
    flexDirection: 'row' as const,
    gap: rv(5),
  },
  timeText: {
    fontSize: rv(11),
    color: theme.colors.textSecondary,
    flexWrap: 'nowrap' as const,
    fontFamily: 'medium' as const,
  },
  unreadBadge: {
    width: 20,
    height: 20,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    backgroundColor: theme.colors.notificationBadge,
    marginRight: 10,
    borderRadius: 20,
  },
  unreadBadgeText: {
    color: theme.colors.notificationText,
    fontSize: rv(11),
    fontFamily: 'bold' as const,
  },
  messageContainer: {
    flexDirection: 'row' as const,
    justifyContent: 'flex-start' as const,
    alignItems: 'flex-start' as const,
  },
  messageWrapper: {
    width: '80%',
    alignItems: 'center' as const,
    flexDirection: 'row' as const,
  },
  messageText: {
    color: theme.colors.textSecondary,
    fontSize: rv(13),
    fontFamily: 'medium' as const,
  },
}));
// Export the container component as the main export
export const ChatCard = ChatCardContainer;

