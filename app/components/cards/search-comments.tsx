import { FunctionComponent } from 'react';
import React from 'react';
import { View, TouchableOpacity, StyleSheet, Image } from 'react-native';
import { CustomText } from '../elements';
import Avatar from 'app/components/elements/Avater';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
  renderers,
} from 'react-native-popup-menu';
import MenuSVG from 'app/assets/svg/menu.svg';
import { Axios } from 'app/api/axios';
import { ShowAlert } from 'app/providers/toast';

import ReportGroupSVG from 'app/assets/svg/reportGroup.svg';
import MuteSVG from 'app/assets/svg/muteGroup.svg';
import UnMuteSVG from 'app/assets/svg/unmuteGroup.svg';
import ExitGroupSVG from 'app/assets/svg/ExitGroup.svg';
import { userId, userToken } from 'app/redux/user/reducer';
import { useSelector } from 'react-redux';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import Document from '../attachments/Document';
import Audio from '../attachments/Audio';
import Video from 'app/components/attachments/Video';
import { timeAgo } from 'app/helpers/time-ago';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import ThumbSVG from '../svgReactComponent/ThumSVG';
import { useEffect, useState } from 'react';
import CommentSVG from '../svgReactComponent/CommentSVG';
import { useTranslation } from 'react-i18next';
import FlagSVG from 'app/assets/svg/flagasInapp.svg';
import { showModal, hideModal } from 'app/providers/modals';
import { useTheme } from 'app/hooks/useTheme';
import { createThemedStyles } from 'app/utils/createThemedStyles';

export type CommentProps = {
  navigation: any;
  item?: any;

  refreshFunction?: () => void;
};

// Container Component
const SearchCommentCardContainer: FunctionComponent<CommentProps> = ({
  item,
  navigation,
  refreshFunction,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [isLiked, setIsLiked] = useState(false);
  const [numLikes, setNumLikes] = useState(0);
  const [isMuted, setIsMuted] = useState(item.muted);
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportDetails, setReportDetails] = useState({});

  function openReportDetails(item: any, type: any) {
    setReportDetails({
      data: { _id: item },
      type,
    });
    setShowReportDialog(true);
  }

  function handleLike() {
    // Toggle like state
    setIsLiked(!isLiked);
    setNumLikes(isLiked ? numLikes - 1 : numLikes + 1);
    // Add API call here if needed
  }

  useEffect(() => {
    if (item.isLiked && item.likes) {
      setIsLiked(item.isLiked);
      setNumLikes(item.likes);
    }

    console.log(JSON.stringify(item.image), '==see topic content');
  }, [item]);

  const { SlideInMenu } = renderers;

  return (
    <SearchCommentCardPresentation
      theme={theme}
      styles={styles}
      item={item}
      navigation={navigation}
      t={t}
      isLiked={isLiked}
      numLikes={numLikes}
      showReportDialog={showReportDialog}
      reportDetails={reportDetails}
      handleLike={handleLike}
      openReportDetails={openReportDetails}
      setShowReportDialog={setShowReportDialog}
      SlideInMenu={SlideInMenu}
    />
  );
};

// Presentation Component
interface SearchCommentCardPresentationProps {
  theme: any;
  styles: any;
  item: any;
  navigation: any;
  t: any;
  isLiked: boolean;
  numLikes: number;
  showReportDialog: boolean;
  reportDetails: any;
  handleLike: () => void;
  openReportDetails: (item: any, type: any) => void;
  setShowReportDialog: (show: boolean) => void;
  SlideInMenu: any;
}

const SearchCommentCardPresentation: React.FC<SearchCommentCardPresentationProps> = ({
  theme,
  styles,
  item,
  navigation,
  t,
  isLiked,
  numLikes,
  showReportDialog,
  reportDetails,
  handleLike,
  openReportDetails,
  setShowReportDialog,
  SlideInMenu,
}) => {

  return (
    <View style={styles.container}>
      <View style={styles.avatarSection}>
        <TouchableOpacity
          onPress={() =>
            navigation.push('Common', {
              screen: 'private-chat',
              params: {
                postid: item.userId.first_name + ' ' + item.userId.last_name,
                userDetails: item.userId,
              },
            })
          }
        >
          <View style={styles.avatarContainer}>
            <Avatar source={item.userId.profile_picture} size={50} />
          </View>
        </TouchableOpacity>
      </View>
      <View style={styles.contentContainer}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'flex-start',
            marginBottom: 5,
          }}
        >
          <View style={{ width: '100%' }}>
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}
            >
              <TouchableOpacity
                onPress={() =>
                  navigation.push('Common', {
                    screen: 'private-chat',
                    params: {
                      userDetails: item.userId,
                    },
                  })
                }
              >
                <CustomText style={styles.userName}>
                  {item.userId.first_name + ' ' + item.userId.last_name}
                </CustomText>
              </TouchableOpacity>
              <View
                style={{
                  width: '30%',
                  alignItems: 'flex-end',
                }}
              >
                <Menu
                  renderer={SlideInMenu}
                  // onSelect={(value) => {
                  //     //onMenuClicked(value)
                  // }}
                >
                  <MenuTrigger>
                    <View
                      style={{
                        alignItems: 'flex-end',
                        flexDirection: 'row',
                        padding: rv(3)
                      }}
                    >
                      <MenuSVG width={15} height={15} />
                    </View>
                  </MenuTrigger>

                  <MenuOptions
                    customStyles={{
                      optionText: [styles.menuText],
                      optionsContainer: [styles.menuOptionsContainer],
                    }}
                  >
                    <View style={styles.menuContent}>
                      {item.userId._id  ?    <MenuOption
                        onSelect={() => {
                          openReportDetails(item._id, 'comment');
                        }}
                      >
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '100%',
                            alignItems: 'center',
                            marginHorizontal: 10,
                          }}
                        >
                          <FlagSVG width={20} />
                          <CustomText style={styles.menuItemText}>
                            Flag comment as inappropriate
                          </CustomText>
                        </View>
                      </MenuOption>  : null }
                   
                    </View>
                  </MenuOptions>
                </Menu>
              </View>
            </View>
            <View style={{ width: '100%' }}>
              <CustomText
                adjustsFontSizeToFit={false}
                numberOfLines={2}
                style={styles.userPosition}
              >
                {item.userId.company_position && item.userId.company
                  ? `${
                      item.userId.company_position?.replace(
                        /(\r\n|\n|\r)/gm,
                        ''
                      ) || 'company'
                    } at ${item.userId.company}`
                  : 'Position at company'}
              </CustomText>
            </View>
            <View
              style={{
                width: '80%',
                alignItems: 'flex-start',
              }}
            >
              <CustomText
                adjustsFontSizeToFit={true}
                numberOfLines={1}
                style={styles.timeText}
              >
                {timeAgo(item.createdAt)}
              </CustomText>
            </View>
          </View>
        </View>

        {/* {Comment} */}
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'flex-start',
          }}
        >
          <View style={{ width: '95%' }}>
            <CustomText style={styles.commentText}>
              {item.comment}
            </CustomText>
          </View>
        </View>

        {/* Comment Medias */}

        {item.image ? (
          <TouchableOpacity
            style={styles.attachment}
            onPress={() =>
              navigation.push('fullScreenImage', {
                image: item.image,
              })
            }
          >
            <Image
              source={{
                uri: item.image,
              }}
              style={styles.commentImage}
              resizeMode='cover'
            />
          </TouchableOpacity>
        ) : null}
        {item.document ? (
          <View style={styles.attachment}>
            <Document
              attachmentSize={item.attachmentSize}
              link={item.document}
              navigation={navigation}
            />
          </View>
        ) : null}

        {item.audio ? (
          <View style={styles.attachment}>
            <Audio
              attachmentSize={item.attachmentSize}
              link={item.audio}
              navigation={navigation}
            />
          </View>
        ) : null}

        {item.video ? (
          <View style={styles.attachment}>
            <Video
              attachmentSize={item.attachmentSize}
              link={item.video}
              navigation={navigation}
            />
          </View>
        ) : null}
        <View
          style={{
            flexDirection: 'row',
            marginTop: 10,
            marginRight: wp('5%'),
            justifyContent: 'space-between',
          }}
        >
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <CommentSVG
              width={20}
              color={theme.colors.accent}
              style={styles.commentIcon}
            />

            <CustomText style={styles.commentsText}>
              {item.noOfComments} {t('Topic_Comments')}
              {item.noOfComments > 1 ? 's' : ''}
            </CustomText>
          </View>

          <TouchableOpacity
            onPress={() => handleLike()}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <ThumbSVG style={{}} color={isLiked ? theme.colors.accent : theme.colors.textPlaceholder} />

            <CustomText style={[styles.likesText, { color: isLiked ? theme.colors.accent : theme.colors.textPlaceholder }]}>
              {numLikes} {t('comments_likes')}
              {numLikes > 1 ? 's' : ''}
            </CustomText>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

// Themed styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    flex: 1,
    flexDirection: 'row' as const,
    justifyContent: 'flex-start' as const,
    alignItems: 'flex-start' as const,
    backgroundColor: theme.colors.cardBackground,
    paddingTop: rv(8),
    paddingHorizontal: rv(15),
  },
  avatarSection: {
    // Empty container style
  },
  avatarContainer: {
    marginBottom: 10,
    marginRight: 15,
  },
  contentContainer: {
    width: '80%',
  },
  userName: {
    color: theme.colors.text,
    fontSize: rv(13),
    fontFamily: 'bold' as const,
  },
  menuOptionsContainer: {
    borderRadius: 15,
    backgroundColor: theme.colors.surface,
  },
  menuContent: {
    margin: 5,
    flexDirection: 'column' as const,
    marginVertical: 10,
    padding: 15,
  },
  menuItemText: {
    color: theme.colors.text,
    fontSize: rv(12),
    paddingLeft: rv(8),
    fontFamily: 'medium' as const,
  },
  menuText: {
    color: theme.colors.textSecondary,
    textAlign: 'center' as const,
    fontSize: 18,
    width: '87%',
    fontFamily: 'regular' as const,
  },
  userPosition: {
    fontSize: rv(10),
    color: theme.colors.textSecondary,
    fontFamily: 'medium' as const,
  },
  timeText: {
    fontSize: rv(9),
    color: theme.colors.textSecondary,
    flexWrap: 'nowrap' as const,
  },
  commentText: {
    color: theme.colors.textSecondary,
    lineHeight: 20,
    fontSize: rv(10),
    fontFamily: 'medium' as const,
  },
  attachment: {
    marginTop: 8,
    width: wp('68%'),
  },
  commentImage: {
    height: hp('30%'),
    borderRadius: wp('2%'),
    borderColor: theme.colors.border,
    borderWidth: wp('0.6%'),
    flex: 1,
    flexDirection: 'column' as const,
  },
  commentIcon: {
    marginRight: 5,
  },
  commentsText: {
    color: theme.colors.accent,
    marginLeft: 3,
    fontSize: rv(9),
    paddingTop: 6,
  },
  likesText: {
    fontSize: rv(9),
    marginLeft: 5,
    marginRight: 40,
    marginTop: 4,
  },
}));

// Export the container component as the main export
export const SearchCommentCard = SearchCommentCardContainer;
