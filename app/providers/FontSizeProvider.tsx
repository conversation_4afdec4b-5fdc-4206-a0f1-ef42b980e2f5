import React, { createContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Font size scale options
export type FontSizeScale = 'small' | 'normal' | 'large' | 'extra-large';

// Font size multipliers for each scale
export const FONT_SIZE_MULTIPLIERS: Record<FontSizeScale, number> = {
  'small': 0.85,
  'normal': 1.0,
  'large': 1.15,
  'extra-large': 1.3,
};

// Font size labels for UI
export const FONT_SIZE_LABELS: Record<FontSizeScale, string> = {
  'small': 'Small',
  'normal': 'Normal',
  'large': 'Large',
  'extra-large': 'Extra Large',
};

// Storage key for font size preference
const FONT_SIZE_STORAGE_KEY = '@app_font_size';
const DEFAULT_FONT_SIZE: FontSizeScale = 'normal';

// Context type
export interface FontSizeContextType {
  fontSizeScale: FontSizeScale;
  fontSizeMultiplier: number;
  setFontSizeScale: (scale: FontSizeScale) => void;
  scaleFontSize: (baseSize: number) => number;
  isLoading: boolean;
}

// Create font size context
export const FontSizeContext = createContext<FontSizeContextType | undefined>(undefined);

interface FontSizeProviderProps {
  children: ReactNode;
}

export const FontSizeProvider: React.FC<FontSizeProviderProps> = ({ children }) => {
  const [fontSizeScale, setFontSizeScaleState] = useState<FontSizeScale>(DEFAULT_FONT_SIZE);
  const [isLoading, setIsLoading] = useState(true);

  // Load saved font size from storage on app start
  useEffect(() => {
    loadSavedFontSize();
  }, []);

  const loadSavedFontSize = async () => {
    try {
      const savedFontSize = await AsyncStorage.getItem(FONT_SIZE_STORAGE_KEY);
      if (savedFontSize && Object.keys(FONT_SIZE_MULTIPLIERS).includes(savedFontSize)) {
        setFontSizeScaleState(savedFontSize as FontSizeScale);
      }
    } catch (error) {
      console.warn('Failed to load saved font size:', error);
      // Default to normal font size if loading fails
      setFontSizeScaleState(DEFAULT_FONT_SIZE);
    } finally {
      setIsLoading(false);
    }
  };

  const saveFontSize = async (scale: FontSizeScale) => {
    try {
      await AsyncStorage.setItem(FONT_SIZE_STORAGE_KEY, scale);
    } catch (error) {
      console.warn('Failed to save font size:', error);
    }
  };

  const setFontSizeScale = (scale: FontSizeScale) => {
    setFontSizeScaleState(scale);
    saveFontSize(scale);
  };

  // Get current font size multiplier
  const fontSizeMultiplier = FONT_SIZE_MULTIPLIERS[fontSizeScale];

  // Function to scale font sizes
  const scaleFontSize = (baseSize: number): number => {
    return Math.round(baseSize * fontSizeMultiplier);
  };

  const contextValue: FontSizeContextType = {
    fontSizeScale,
    fontSizeMultiplier,
    setFontSizeScale,
    scaleFontSize,
    isLoading,
  };

  return (
    <FontSizeContext.Provider value={contextValue}>
      {children}
    </FontSizeContext.Provider>
  );
};
