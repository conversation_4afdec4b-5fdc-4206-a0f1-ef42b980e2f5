import React, { FunctionComponent } from 'react';
import { View, StyleSheet, ImageBackground,SafeAreaView, Image } from 'react-native';
import { DrawerItem } from '@react-navigation/drawer';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { CustomText } from 'app/components/elements';
import Avatar from 'app/components/elements/Avater';
import {
  CreateGroupSVG,
  HelpdeskSVG,
  // LogoutSVG,
  PostATopicSVG,
  ProfileSVG,
  SettingsSVG,
  UpgradeSVG,
  BulbSVG,
  BuyCreditSVG
} from 'app/providers/svg/loader';

import { useSelector } from 'react-redux';
import { userAuthInfo } from 'app/redux/user/reducer';
import { logout } from 'app/redux/user/hooks';
import { useTranslation } from 'react-i18next';
import { runtimeVersion } from 'expo-updates';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import LogoutSVG from '@/app/assets/svg/LogoutinRed.svg';
import { useTheme } from 'app/hooks/useTheme';
import { createThemedStyles } from 'app/utils/createThemedStyles';

type CustomSidebarMenuProps = {
  navigation?: any;
  props: any;
};

const Sidebar: FunctionComponent<CustomSidebarMenuProps> = ({props}) => {
  const profile = useSelector(userAuthInfo);
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = createStyles(theme);

  return (
    <View style={[{ flex: 1 }, styles.container]}>
      <ImageBackground
        source={
          profile && profile.header_image
            ? { uri: profile.header_image }
            : require('app/assets/default_profile_img.png')
        }
        style={{
          width: '100%',
          height: hp('12%'),
          // justifyContent: 'center',
          // alignItems: 'center',
        }}
      >
        {profile ? (
          <SafeAreaView
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingTop: rv(20),
              paddingLeft: rv(30)

            }}
          >
            <Avatar source={profile.profile_picture} size={hp('6%')} />
            <CustomText
              style={{
                color: 'white',
                fontSize: rv(13),
                marginLeft: 20,
                fontFamily: 'bold'

              }}
              // textType='bold'
            >
              {`${profile.first_name} ${profile.last_name}`.length > 9 ? `${`${profile.first_name} ${profile.last_name}`.slice(0, 9)}...` : `${profile.first_name} ${profile.last_name}`}
            </CustomText>
          </SafeAreaView>
        ) : null}
      </ImageBackground>
      <View
        style={{
          flex: 1,
          flexDirection: 'column',
          paddingHorizontal: wp('5%'),
          marginVertical: 10,
        }}
      >
        <DrawerItem
          label={t("SideNav_profile")}
          labelStyle={[styles.drawerItemLabel]}
          onPress={() =>
            props.navigation.navigate('Profile', {
              userDetails: { profile: true },
            })
          }
          icon={() => <ProfileSVG width={'20'} height={'20'} />}
        />

        <DrawerItem
          label="Share a post"
          labelStyle={styles.drawerItemLabel}
          onPress={() => props.navigation.navigate('PostTopic')}
          icon={() => <PostATopicSVG width={'20'} height={'20'} />}
        />

        <DrawerItem
          label={t("SideNav_createGroup")}
          labelStyle={styles.drawerItemLabel}
          onPress={() => props.navigation.navigate('CreateGroup')}
          icon={() => <CreateGroupSVG width={'20'} height={'20'} />}
        />
        <DrawerItem
          label="Trivia"
          labelStyle={styles.drawerItemLabel}
          onPress={() => props.navigation.navigate('QuizScreen')}
          icon={() => <BulbSVG width={'20'} height={'20'} />}
        />

        <DrawerItem
          label={t("SideNav_settings")}
          labelStyle={styles.drawerItemLabel}
          onPress={() => props.navigation.navigate('Settings')}
          icon={() => <SettingsSVG width={'20'} height={'20'} />}
        />

        <DrawerItem
          label="Membership Access"
          labelStyle={styles.drawerItemLabel}
          onPress={() => props.navigation.navigate('Credits')}
          icon={() => <BuyCreditSVG width={'20'} height={'20'} />}
        />
        {/*
        <DrawerItem
          label='Donate'
          labelStyle={{
  fontFamily: 'regular',
              fontSize: rv(13),
            color: '#696969',
            lineHeight: 20,
            marginVertical: 5,
          }}
          onPress={() => props.navigation.navigate('Donate')}
          icon={() => <UpgradeSVG width={'20'} height={'20'} />}
        /> */}

        <DrawerItem
          label={t("SideNav_helpDesk")}
          labelStyle={styles.drawerItemLabel}
          onPress={() => props.navigation.navigate('Helpdesk')}
          icon={() => <HelpdeskSVG width={'20'} height={'20'} />}
        />

        <DrawerItem
          label={t("SideNav_logout")}
          labelStyle={styles.logoutLabel}
          onPress={() => logout()}
          icon={() =>   <LogoutSVG  width={'20'} height={'20'} />
        }
        />

        {/* <DrawerItemList {...props} /> */}
      </View>
    </View>
  );
};

const createStyles = createThemedStyles((theme) => ({
  container: {
    backgroundColor: theme.colors.background,
  },
  drawerItemLabel: {
    fontFamily: 'regular',
    fontSize: rv(13),
    color: theme.colors.textSecondary,
    lineHeight: 20,
    marginVertical: 5,
  },
  logoutLabel: {
    fontFamily: 'regular',
    fontSize: rv(13),
    color: theme.colors.error,
    lineHeight: 20,
    marginVertical: 5,
  },
  sideMenuProfileIcon: {
    resizeMode: 'contain',
    width: wp('30%'),
    height: hp('10%'),
    marginLeft: 20,
    alignSelf: 'flex-start',
  },
  iconStyle: {
    width: 15,
    height: 15,
    marginHorizontal: 5,
  },
  customItem: {
    padding: rv(16),
    flexDirection: 'row',
    alignItems: 'center',
  },
}));

export default Sidebar;
