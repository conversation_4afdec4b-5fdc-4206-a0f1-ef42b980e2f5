/**
 * Theme system exports
 * Central location for all theme-related imports
 */

// Types
export type {
  Theme,
  ThemeMode,
  ThemeColors,
  ThemeContextType,
  ThemeSpacing,
  ThemeBorderRadius,
  ThemeShadows
} from 'app/types/theme';

// Font Size Types
export type {
  FontSizeScale,
  FontSizeContextType,
  FontSizeConfig
} from 'app/types/fontSize';

// Theme configuration
export { 
  lightTheme, 
  darkTheme, 
  getTheme,
  colors,
  spacing,
  borderRadius,
  shadows
} from 'app/assets/themes';

// Hooks
export {
  useTheme,
  useThemeValues,
  useThemeMode,
  useThemeActions
} from 'app/hooks/useTheme';
export {
  useFontSize,
  useFontSizeScale,
  useFontSizeMultiplier
} from 'app/hooks/useFontSize';

// Providers
export { ThemeProvider, ThemeContext } from 'app/providers/ThemeProvider';
export { FontSizeProvider, FontSizeContext } from 'app/providers/FontSizeProvider';

// Utilities
export { 
  createThemedStyles,
  createResponsiveThemedStyles,
  mergeThemeColors,
  getPlatformThemedValue,
  createConditionalStyles
} from 'app/utils/createThemedStyles';

// Constants
export {
  DEFAULT_THEME_MODE,
  THEME_STORAGE_KEY,
  THEME_TRANSITION_DURATION,
  OPACITY,
  Z_INDEX,
  BORDER_WIDTH,
  FONT_WEIGHT,
  COMPONENT_HEIGHT,
  BREAKPOINTS,
  ANIMATIONS
} from 'app/constants/theme';

// Components
export { ThemedStatusBar } from 'app/components/ThemedStatusBar';
export { ThemeToggleButton } from 'app/components/ThemeToggleButton';
export { ThemeSelector } from 'app/components/ThemeSelector';
export { default as ThemedComponentExample } from 'app/components/examples/ThemedComponentExample';
