import { StyleSheet } from 'react-native';
import { Theme } from 'app/types/theme';

/**
 * Utility function to create themed styles
 * This function takes a style creator function that receives the theme
 * and returns a StyleSheet object
 * 
 * @param styleCreator - Function that takes theme and returns style object
 * @returns Function that takes theme and returns StyleSheet
 */
export const createThemedStyles = <T extends Record<string, any>>(
  styleCreator: (theme: Theme) => T
) => {
  return (theme: Theme) => StyleSheet.create(styleCreator(theme));
};

/**
 * Helper function to create responsive themed styles
 * Includes responsive value function for consistency
 */
export const createResponsiveThemedStyles = <T extends Record<string, any>>(
  styleCreator: (theme: Theme, rv: (value: number) => number) => T,
  responsiveValue: (value: number) => number
) => {
  return (theme: Theme) => StyleSheet.create(styleCreator(theme, responsiveValue));
};

/**
 * Utility to merge theme colors with custom overrides
 * Useful for component-specific color variations
 */
export const mergeThemeColors = (theme: Theme, overrides: Partial<Theme['colors']>) => {
  return {
    ...theme,
    colors: {
      ...theme.colors,
      ...overrides,
    },
  };
};

/**
 * Helper to get platform-specific theme values
 */
export const getPlatformThemedValue = <T>(
  theme: Theme,
  iosValue: T,
  androidValue: T,
  defaultValue: T = iosValue
): T => {
  const Platform = require('react-native').Platform;
  
  if (Platform.OS === 'ios') {
    return iosValue;
  } else if (Platform.OS === 'android') {
    return androidValue;
  }
  
  return defaultValue;
};

/**
 * Helper to create conditional styles based on theme mode
 */
export const createConditionalStyles = <T extends Record<string, any>>(
  lightStyles: T,
  darkStyles: T
) => {
  return (theme: Theme) => {
    const styles = theme.mode === 'dark' ? darkStyles : lightStyles;
    return StyleSheet.create(styles);
  };
};

export default createThemedStyles;
