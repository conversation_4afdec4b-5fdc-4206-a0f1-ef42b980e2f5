import {
  View,
  Text,
  TextInput,
  Image,
  TouchableOpacity,
  StyleSheet,
  TouchableHighlight,
  ScrollView,
  SafeAreaView
} from 'react-native';

import React, {
  FunctionComponent,
  useEffect,
  useState,
  useContext,
} from 'react';

import HeaderTitle from '../../components/HeaderTitle';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { CustomButton, CustomText } from '../../components/elements';
import { topic } from 'app/api';
import { ShowAlert } from 'app/providers/toast';
import { useTranslation } from 'react-i18next';
import { showModal, hideModal } from 'app/providers/modals';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useTheme } from 'app/theme';

type PostATopicProps = {
  navigation: any;
  route: any;
};

const EditTopic: FunctionComponent<PostATopicProps> = ({
  navigation,
  route,
}) => {
  const { topicData } = route.params;
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const [loading, setLoading] = useState(false);
  const [content, setContent] = useState('');

  useEffect(() => {
    if (topicData && topicData.content) {
      setContent(topicData.content);
    }
  }, [topicData]);

  const handleSubmitPost = () => {
    if (!content) {
      showModal({
        modalVisible: true,
        title: 'Alert',
         message: "Topic can not empty",
        setModalVisible: hideModal, // Function to close the modal
        type: 'alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
      
      });
      return;
    }

    if (topicData && topicData._id) {

      //Show Loader    
      setLoading(true);
      topic.editTopic(topicData._id, { content }).then(() => {
        showModal({
          modalVisible: true,
          title: 'Alert',
           message: "Topic updated",
          setModalVisible: hideModal, // Function to close the modal
          type: 'success-alert',
          handleConfirm: () => {
            console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Alert handled!');
            hideModal();
          },
        
        });

        // ShowAlert({ type: 'info', className: 'Success', message: "Topic Updated Successfully" })
        navigation.goBack();
      }).finally(() => {
        setLoading(false);
      })
    }
  };

  return (
    <ScrollView
      style={{
        flex: 1,
        backgroundColor: theme.colors.background,
      }}
    >
      <SafeAreaView>
      <HeaderTitle title='Edit topic' navigation={navigation} />

      {topicData ? (
        <View
          style={{
            paddingHorizontal: wp('10%'),
            flexDirection: 'column',
          }}
        >
          <TextInput
            multiline={true}
            numberOfLines={10}
            placeholder={'Type your topic'}
            placeholderTextColor={theme.colors.textPlaceholder}
            style={{
              padding: rv(10),
              backgroundColor: theme.colors.inputBackground,
              height: hp('23%'),
              textAlignVertical: 'top',
              fontSize: rv(13),
              color: theme.colors.text,
              lineHeight: 20,
              fontFamily: 'semiBold',
              borderRadius: 10,
            }}
            underlineColorAndroid='transparent'
            onChangeText={(contents) => setContent(contents)}
            maxLength={200}
            value={content}
          />



          <CustomButton
            label={t('Topic_EditTopic')}
            buttonTheme='primary'
            onPress={() => handleSubmitPost()}
            loading={loading}
          />

          {/* <TouchableHighlight
            onPress={() => handleSubmitPost()}
            style={{
              backgroundColor: 'gold',
              height: 60,
              width: '100%',
              marginTop: 15,
              marginBottom: 20,
              borderRadius: 30,
              paddingVertical: 15,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <CustomText
              style={{
                color: 'black',
                textAlign: 'center',
                fontSize: 16,
              }}
            >
              Edit Topic
            </CustomText>
          </TouchableHighlight> */}
        </View>
      ) : null}
      </SafeAreaView>
    </ScrollView>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  attachmentBox: {
    borderRadius: 40,
    height: 60,
    width: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    marginHorizontal: 16,
  },
  title: {
    textAlign: 'center',
    marginVertical: 8,
    fontSize: rv(13),
    color: theme.colors.text,
    fontFamily: 'bold'
    },
  fixToText: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  separator: {
    marginVertical: 8,
    borderBottomColor: theme.colors.border,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  signup: {
    height: 60,
    borderColor: theme.colors.border,
    borderWidth: 1,
    borderRadius: 40,
    paddingLeft: 20,
    backgroundColor: theme.colors.surface,
    marginBottom: 15,
    borderStyle: 'solid',
    marginTop: 10,
  },
  replycomment: {
    flex: 1,
    flexDirection: 'row',
    height: 45,
    borderColor: theme.colors.border,
    borderWidth: 1,
    borderRadius: 40,
    paddingLeft: 20,
    backgroundColor: theme.colors.surfaceVariant,
    marginBottom: 15,
    borderStyle: 'solid',
    marginTop: 10,
    position: 'relative',
  },
  reply: {
    flexDirection: 'row',
    width: '90%',
    justifyContent: 'space-around',
    borderColor: theme.colors.border,
    borderWidth: 1,
    borderRadius: 10,
    paddingLeft: 20,
    backgroundColor: theme.colors.surface,
    marginBottom: 15,
    borderStyle: 'solid',
    marginTop: 10,
  },
  itemList: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    backgroundColor: theme.colors.surface,
    paddingTop: hp('2%'),
    borderRadius: 20,
    margin: 20,
  },
  testItems: {
    flex: 1,
    flexDirection: 'row',
    height: 60,
    borderColor: theme.colors.border,
    borderWidth: 0.5,
    borderRadius: 5,
    paddingLeft: 20,
    backgroundColor: theme.colors.surfaceVariant,
    marginBottom: 10,
    paddingTop: hp('2%'),
  },
  testItemsTitle: { width: wp('62%'), paddingTop: hp('0.8%') },
  medicalinput: {
    height: 50,
    borderColor: theme.colors.border,
    borderWidth: 2,
    marginBottom: 20,
    borderRadius: 33,
    paddingLeft: 20,
    backgroundColor: theme.colors.inputBackground,
  },
  medicalinputarea: {
    height: 120,
    borderColor: theme.colors.border,
    borderWidth: 2,
    marginBottom: 20,
    borderRadius: 33,
    paddingLeft: 20,
    backgroundColor: theme.colors.inputBackground,
    justifyContent: 'flex-start',
  },
  icontext: {
    paddingTop: 30,
    color: theme.colors.textSecondary,
    fontSize: rv(13),
    // fontWeight: '200',
    textAlign: 'center',
    // fontFamily: 'Segoe UI',
    fontFamily: 'medium'
  },
  profilename: {
    // fontFamily: 'Segoe UI',
    fontStyle: 'normal',
    // fontWeight: '600',
    fontSize: rv(12),
    lineHeight: 27,
    fontFamily: 'semiBold'
  },
  touchbtn: {
    backgroundColor: theme.colors.accent,
    height: 50,
    width: 150,
    borderColor: theme.colors.accent,
    borderWidth: 2,
    borderRadius: 33,
    paddingLeft: 20,
    paddingRight: 20,
    paddingTop: 10,
  },
  consultview: {
    flex: 1,
    width: 500,
    flexDirection: 'row',
    alignItems: 'stretch',
    borderStyle: 'solid',
    borderBottomColor: theme.colors.border,
    borderBottomWidth: 2,
    paddingTop: 10,
    marginLeft: 16,
    paddingBottom: 10,
  },
  medicalview: {
    flex: 1,
    flexDirection: 'column',
    borderStyle: 'solid',
    paddingTop: 10,
    marginLeft: 16,
    marginRight: 17,
  },
  scheduleview: {
    flex: 1,
    flexDirection: 'column',
    alignContent: 'stretch',
    marginLeft: 16,
    marginRight: 17,
  },
  slide: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.info,
  },
  image: {
    width: 400,
    height: 481,
    // marginVertical: 12,
  },
  text: {
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontSize: rv(12),
    width: '87%',
    fontFamily: 'medium'
    // fontFamily: 'Helvetica',
  },
  splashbtn: {
    alignContent: 'center',
    alignSelf: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    height: 60,
    width: 250,
    borderColor: theme.colors.surface,
    borderWidth: 2,
    marginTop: 1,
    marginBottom: 20,
    borderRadius: 30,
    paddingTop: 25,
    fontStyle: 'normal',
    fontFamily: 'bold',
        fontSize: rv(12),
    lineHeight: 14,
    textAlign: 'center',
    color: theme.colors.primary,
    display: 'flex',
    marginLeft: 20,
    marginRight: 20,
  },
  titleAccount: {
    fontSize: rv(13),
    color: theme.colors.text,
    fontFamily: 'bold',
        textAlign: 'center',
    marginBottom: 20,
  },
  errorTextStyle: {
    color: theme.colors.error,
    textAlign: 'center',
    fontSize: rv(12),
  },
  tabStyle: {},
  scrollStyle: {
    backgroundColor: theme.colors.background,
    paddingLeft: 65,
    paddingRight: 65,
    // justifyContent: 'center',
  },
  tabBarTextStyle: {
    fontSize: rv(12),
    fontWeight: 'normal',
  },
  underlineStyle: {
    height: 3,
    backgroundColor: theme.colors.error,
    borderRadius: 3,
    width: 15,
  },
  topbar: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surfaceVariant,
    paddingTop: 15,
  },
  trigger: {
    padding: 5,
    margin: 5,
  },
  triggerText: {
    color: theme.colors.surface,
  },
  disabled: {
    color: theme.colors.textDisabled,
  },
  divider: {
    marginVertical: 5,
    marginHorizontal: 2,
    borderBottomWidth: 1,
    borderColor: theme.colors.border,
  },
  logView: {
    flex: 1,
    flexDirection: 'column',
  },
  logItem: {
    flexDirection: 'row',
    padding: 8,
  },
  slideInOption: {
    padding: 5,
  },
  bottom: {
    position: 'absolute',
    bottom: 10,
    right: 10,
  },
  touchableOpacityStyle: {
    position: 'absolute',
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    right: 30,
    bottom: 30,
  },
  floatingButtonStyle: {
    resizeMode: 'contain',
    width: '125%',
    height: '125%',
    //backgroundColor:'black'
  },
  actionButtonIcon: {
    fontSize: rv(12),
    height: 22,
    color: theme.colors.surface,
  },
  profile: {
    height: 60,
    borderColor: theme.colors.border,
    borderWidth: 0.5,
    borderRadius: 5,
    paddingLeft: 20,
    backgroundColor: theme.colors.surfaceVariant,
    marginBottom: 10,
  },
  topicStyle: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    backgroundColor: theme.colors.surfaceVariant,
    paddingTop: 20,
    borderBottomColor: theme.colors.border,
    borderBottomWidth: 1,
    marginTop: 10,
    marginRight: 10,
    marginLeft: 10,
    borderRadius: 15,
  },
  label: {
    marginVertical: 5,
  },
});

export default EditTopic;
