import React from 'react';
import { View, FlatList, TouchableOpacity, Image } from 'react-native';
import { CustomText } from 'app/components/elements';
import HeaderTitle from 'app/components/HeaderTitle';
import Avatar from 'app/components/elements/Avater';
import GroupParticipantItem from 'app/components/FlatListItems/GroupParticipant';
import { useSelector } from 'react-redux';
import { userId } from 'app/redux/user/reducer';
import CreatorSVG from 'app/assets/svg/creator.svg';
import MemberSVG from 'app/assets/svg/members.svg';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useGetGroup } from 'app/redux/group/hooks';
import { useTranslation } from 'react-i18next';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTags, useIsAdmin } from 'app/redux/group/hooks';
import { useTheme } from 'app/theme';

const Loading = require('app/assets/loading.gif');

const GroupInfo = ({ navigation, route }: { navigation: any; route: any }) => {
  const myId = useSelector(userId);
  const { groupDetails } = route.params;
  const groupId = groupDetails?._id || null;
  const { theme } = useTheme();

  const { data: group, isLoading: loading } = useGetGroup(groupId);
  const { tags, loadingTags } = useTags();
  const isAdmin = useIsAdmin(group, myId);
  const { t } = useTranslation();

  if (loading || loadingTags) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: theme.colors.background,
        }}
      >
        <Image
          source={Loading}
          style={{
            width: rv(50),
            height: rv(50),
          }}
        />
      </View>
    );
  }

  if (!group) {
    return null;
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <HeaderTitle
        title={
          group.name.length > 17 ? `${group.name.slice(0, 17)}...` : group.name
        }
        navigation={navigation}
      />
      <View style={{ flex: 1, marginHorizontal: 30 }}>
        <View style={{ flexDirection: 'column' }}>
          {/* Group Image and Edit Button */}
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{ width: '70%' }}>
              <TouchableOpacity
                onPress={() => {
                  if (group.image) {
                    navigation.push('Common', {
                      screen: 'fullscreen-image',
                      params: { image: group.image },
                    });
                  }
                }}
              >
                <Avatar size={40} type='group' source={group.image} />
              </TouchableOpacity>
            </View>
            <View style={{ width: '30%' }}>
              {isAdmin && (
                <TouchableOpacity
                  onPress={() => navigation.push('edit-group', { group })}
                  style={{
                    borderColor: theme.colors.primary,
                    borderWidth: rv(1),
                    borderRadius: rv(20),
                    justifyContent: 'center',
                    alignItems: 'center',
                    padding: rv(7),
                    paddingHorizontal: rv(10),
                  }}
                >
                  <CustomText
                    adjustsFontSizeToFit
                    numberOfLines={1}
                    // textType='semi-bold'
                    style={{ fontSize: rv(12),
                      fontFamily: 'semiBold'


                     }}
                  >
                    Edit Group
                  </CustomText>
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Group Name and Description */}
          <CustomText
            style={{ marginTop: 10, fontSize: rv(12),               fontFamily: 'bold'
            }}
            // textType='bold'
          >
            {group.name}
          </CustomText>
          <View style={{ marginTop: 5 }}>
            <CustomText style={{ fontSize: rv(12),    
             fontFamily: 'semiBold'
 }} 
//  textType='semi-bold'
 >
              {t('Description:')}
            </CustomText>
            <CustomText style={{ fontSize: rv(12),
               fontFamily: 'medium' }}>
              {group.description}
            </CustomText>
          </View>

          {/* Group Creator */}
          <View style={{ flexDirection: 'row', marginTop: 20 }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <CreatorSVG width={22} height={22} />
              <CustomText style={{ marginLeft: 4, fontSize: rv(12),   fontFamily: 'medium'
 }}>
                {t('GroupDesc_creator')}:
              </CustomText>
            </View>
            <TouchableOpacity
              onPress={() => {
                if (myId !== group.createdBy._id) {
                  navigation.navigate('Common', {
                    screen: 'private-chat',
                    params: { userDetails: group.createdBy },
                  });
                }
              }}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginLeft: 10,
              }}
            >
              <Avatar size={22} source={group.createdBy.profile_picture} />
              <CustomText style={{ marginLeft: 4, fontSize: rv(12), fontFamily: 'medium' }}>
                {group.createdBy.first_name} {group.createdBy.last_name}
              </CustomText>
            </TouchableOpacity>
          </View>

          {/* Group Members */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginTop: 10,
            }}
          >
            <MemberSVG width={22} height={22} />
            <CustomText
              style={{ marginLeft: 4, fontSize: rv(12), color: theme.colors.textSecondary, fontFamily: 'medium' }}
            >
              {t('Groups_members')} ({group.participants.length} member
              {group.participants.length > 1 ? 's' : ''})
            </CustomText>
          </View>

          {/* Participants List */}
          <FlatList
            style={{ marginTop: 20 }}
            data={group.participants}
            keyExtractor={(item) => item._id}
            renderItem={({ item }) => (
              <TouchableOpacity
                onPress={() => {
                  if (myId !== item._id) {
                    navigation.push('Common', {
                      screen: 'private-chat',
                      params: { userDetails: item },
                    });
                  }
                }}
              >
                <GroupParticipantItem item={item} navigation={navigation} />
              </TouchableOpacity>
            )}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default GroupInfo;
