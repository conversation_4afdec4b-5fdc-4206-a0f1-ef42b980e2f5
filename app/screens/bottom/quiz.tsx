import React, { useState, useEffect, useRef } from 'react';
import {
  Modal,
  View,
  Text,
  Button,
  Animated,
  TouchableOpacity,
  Image,
  StyleSheet,
} from 'react-native';
import * as Animatable from 'react-native-animatable';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { CustomText } from 'app/components/elements';
import {
  useGetCredit,
  useGetCreditTransactionHistory,
} from 'app/redux/credit/hook';
import { SafeAreaView } from 'react-native-safe-area-context';
import FloatingAddSVG from 'app/assets/svg/next.svg';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import Animation from 'app/components/animation';
import { useGetQuestions, useChangeUserPennytots } from 'app/redux/quiz/hook';
import { useNavigation } from '@react-navigation/native';
import { useFocusEffect } from '@react-navigation/native';
import CustomModal from 'app/components/elements/Modals';
import { logout } from 'app/redux/user/hooks';
import {
  BulbSVG,
  HelpdeskSVG,
  NextArrowSVG,
  GameModeSVG,
  BuyCreditSVG,
} from 'app/providers/svg/loader';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import { useTheme, createThemedStyles } from 'app/theme';

const Loading = require('../../assets/loading.gif');
// const Wrong1 = require('app/assets/times-up.png');
const Wrong2 = require('app/assets/gifs/wrong2.gif');
const Wrong3 = require('../../assets/gifs/angry-furious.gif');
const Wrong4 = require('../../assets/gifs/bad-hair.gif');
const Wrong5 = require('../../assets/gifs/bernie-mac-nervous.gif');
const Wrong6 = require('../../assets/gifs/well-damn.gif');
const Wrong7 = require('../../assets/gifs/martin.gif');
const Wrong8 = require('../../assets/gifs/no-chris-rock.gif');
const Wrong9 = require('../../assets/gifs/no-machaizelli-kahey.gif');
const Wrong10 = require('../../assets/gifs/wow-amazing.gif');
const Wrong11 = require('../../assets/gifs/no-nope-nope-nope.gif');
const Wrong12 = require('../../assets/gifs/no-richard-lane.gif');
const Wrong13 = require('../../assets/gifs/nope-kenan-thompson.gif');
const Wrong14 = require('../../assets/gifs/nope-terry-jeffords.gif');
const Wrong15 = require('../../assets/gifs/oh-come-on-captain-ray-holt.gif');
const Wrong16 = require('../../assets/gifs/oh-no-jeremy.gif');
const Wrong17 = require('../../assets/gifs/orange-is-the-new-black-oitnb.gif');
const Right1 = require('../../assets/gifs/correct1.gif');
import { widthPercentageToDP as wp } from 'react-native-responsive-screen';

const OptionButton = ({
  option,
  onPress,
  isCorrect,
  isSelected,
  correctAnswer,
  canClick, // New prop to control when to show the correct answer
  gameOver,
  style,
}: any) => {
  const colors = [
    {
      background: '#55804C',
      backgroundColor: '#FED830',
    },
    {
      background: '#FFFFFF',
      backgroundColor: '#000000',
    },
    // Add more color combinations here
  ];

  const [currentColor, setCurrentColor] = useState(colors[0]);
  let randomIndex = 0;
  useEffect(() => {
    const intervalId = setInterval(() => {
      if (randomIndex === 0) {
        randomIndex = 1;
      } else {
        randomIndex = 0;
      }
      setCurrentColor(colors[randomIndex]);
    }, 500);

    return () => clearInterval(intervalId);
  }, [randomIndex]);

  return (
    <TouchableOpacity
      onPress={() => {
        // Call the onPress function provided in the props
        onPress();
      }}
      style={{
        backgroundColor:
          isCorrect && isSelected // Show correct answer color if the option is correct and either selected or showAnswer is true
            ? currentColor.background
            : isSelected
            ? '#FE3233'
            : isCorrect !== null && option === correctAnswer
            ? currentColor.background
            : '#FFFFFF',
        height: rv(50),
        borderRadius: rv(12),
        borderColor: isCorrect && isSelected ? currentColor.background : '#163E23',
        borderWidth: isCorrect && isSelected  ? 0
        : isSelected ? 0 : 1,

        paddingHorizontal: rv(30),
        alignItems: 'center',
        justifyContent: 'center',
        fontFamily: 'regular',
        margin: rv(3),
        marginRight: rv(4),
        opacity: gameOver
          ? 0.5
          : isCorrect === null
          ? 1
          : isSelected
          ? 1
          : isCorrect !== null && option === correctAnswer
          ? 1
          : 0.5,
        ...style,
      }}
      disabled={isCorrect !== null || gameOver}
    >
      <Text
        style={{
          color: isCorrect && isSelected 
          ? 'white' 
          : isSelected 
          ? 'white' 
          : isCorrect !== null && option === correctAnswer 
          ? currentColor.background 
            ? 'white' 
            : 'black' 
          : 'black',
          textAlign: 'center',
          fontSize: rv(11),
          fontFamily: 'semiBold',
        }}
      >
        {option}
      </Text>
    </TouchableOpacity>
  );
};

// Presentation component props
interface QuizPresentationProps {
  theme: any;
  styles: any;
  route: any;
  modalVisible: boolean;
  credits: any;
  type: string;
  isFetching: boolean;
  question: string;
  options: string[];
  selectedOption: any;
  isCurrentSelectionCorrect: any;
  correctAnswer: string;
  gameOver: boolean;
  canClick: boolean;
  image: any;
  end: boolean;
  goToNext: boolean;
  t: any;
  handleOptionSelect: (option: any) => void;
  handleNextQuestion: () => void;
  NavigateToCreditPage: () => void;
  NavigateToCreditPagePurchase: () => void;
  setModalVisible: (visible: boolean) => void;
  navigation: any;
}

// Create themed styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingHorizontal: rv(20),
  },
  loadingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignContent: 'center',
  },
  contentContainer: {
    flex: 1,
  },
  questionText: {
    marginTop: rv(15),
    fontSize: rv(11.5),
    fontFamily: 'semiBold',
    color: theme.colors.textSecondary,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: rv(28),
  },
  nextButton: {
    position: 'absolute',
    bottom: rv(5),
    right: rv(20),
  },
  nextButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    width: rv(100),
    height: rv(44),
    borderRadius: 12,
  },
  nextButtonText: {
    fontFamily: 'semiBold',
    fontSize: rv(13),
    color: theme.colors.background,
  },
}));

// Container Component (handles logic, state, theme)
const QuizContainer = ({ route }: any) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const navigation = useNavigation();

  // State management
  const [optionSelected, setOptionSelected] = useState(false);
  const [isNavigatedQuestion, setIsNavigatedQuestion] = useState(false);
  const [navigatedQuestionData, setNavigatedQuestionData] = useState(null);
  const [data1, setData1] = useState<any>(null);
  const [localLoading, setLocalLoading] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [goToNext, setGoToNext] = useState(false);
  const [score, setScore] = useState(0);
  const [gameOver, setGameOver] = useState(false);
  const [isCorrect, setIsCorrect] = useState<any>(null);
  const [isCurrentSelectionCorrect, setIsCurrentSelectionCorrect] = useState<any>(null);
  const [showScoreMessage, setShowScoreMessage] = useState(false);
  const [image, setImage] = useState(null);
  const [end, setEnd] = useState(false);
  const [canClick, setCanClick] = useState(true);
  const [canPlay, setCanPlay] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [type, setType] = useState<any>('');
  const [clickCount, setClickCount] = useState(0);
  const [hasActiveQuestion, setHasActiveQuestion] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Hooks
  const {
    data: quizData,
    isLoading,
    refetch: getNextQuestion,
    isFetching,
  } = useGetQuestions();
  const { mutate: changeUserPennytots } = useChangeUserPennytots();
  const { data: credits, refetch } = useGetCredit();

  const displayQuestionData = isNavigatedQuestion ? navigatedQuestionData : quizData;
  const {
    question,
    options,
    answer: correctAnswer,
  } = displayQuestionData || {};
  const clickThreshold = 15;

  // Helper functions
  const handleButtonClick = async () => {
    try {
      const newClickCount = clickCount + 1;
      setClickCount(newClickCount);
      await AsyncStorage.setItem('clickCount', newClickCount.toString());
    } catch (error) {
      console.error('Failed to save click count:', error);
    }
  };

  const resetClickCount = async () => {
    try {
      setClickCount(0);
      await AsyncStorage.setItem('clickCount', '0');
    } catch (error) {
      console.error('Failed to reset click count:', error);
    }
  };

  const getRandomWrongImage = () => {
    const images = [
      Wrong2, Wrong3, Wrong4, Wrong5, Wrong6, Wrong7, Wrong8, Wrong9,
      Wrong10, Wrong11, Wrong12, Wrong13, Wrong14, Wrong15, Wrong16, Wrong17,
    ];
    const randomIndex = Math.floor(Math.random() * images.length);
    return images[randomIndex];
  };

  const getRandomCorrectImage = () => {
    const images = [Right1];
    const randomIndex = Math.floor(Math.random() * images.length);
    return images[randomIndex];
  };

  const NavigateToCreditPage = () => {
    setModalVisible(!modalVisible);
    navigation.navigate('Credit' as never);
  };

  const NavigateToCreditPagePurchase = () => {
    setModalVisible(!modalVisible);
    navigation.navigate('Credit', { message: 'paramValue' });
  };
  // Main logic functions
  const handleOptionSelect = async (option: any) => {
    setOptionSelected(true);
    handleButtonClick();

    const isCorrect = option === correctAnswer;
    if (!canPlay) {
      setModalVisible(true);
      return;
    }
    setSelectedOption(option);
    try {
      const isAnswerCorrect = option === correctAnswer;
      setIsCorrect(isAnswerCorrect);

      setEnd(true);
      setShowScoreMessage(true);
      setTimeout(() => {
        setShowScoreMessage(false);
      }, 2500);
      setGoToNext(true);
      // Mark that user has interacted with this question
      setHasActiveQuestion(true);

      if (isAnswerCorrect) {
        setIsCurrentSelectionCorrect(true);
        setImage(getRandomCorrectImage());
        await changeUserPennytots('increase');
        refetch();
      } else {
        setIsCurrentSelectionCorrect(false);
        setImage(getRandomWrongImage());
        setCanClick(false);
        setTimeout(() => {
          setIsCorrect(null);
        }, 2500);
        await changeUserPennytots('reduce');
        refetch();
      }
    } catch (error) {
      console.error('Error submitting answer:', error);
      setIsCurrentSelectionCorrect(true);
    }
  };

  const handleNextQuestion = () => {
    // Batch state updates to prevent navigation glitches
    const resetQuestionState = () => {
      if (isNavigatedQuestion) {
        setIsNavigatedQuestion(false);
        setNavigatedQuestionData(null);
      }
      setGoToNext(false);
      setEnd(false);
      setImage(null);
      setSelectedOption(null);
      setIsCorrect(null);
      setIsCurrentSelectionCorrect(null);
      setCanClick(true);
      setHasActiveQuestion(false); // Reset active question state
    };

    resetQuestionState();

    // Small delay to ensure state is updated before fetching new question
    setTimeout(() => {
      getNextQuestion();
      setHasActiveQuestion(true); // Mark that we have a new active question
    }, 50);
  };

  // Effects
  useEffect(() => {
    const getClickCount = async () => {
      try {
        const storedClickCount = await AsyncStorage.getItem('clickCount');
        if (storedClickCount !== null) {
          setClickCount(parseInt(storedClickCount));
        }
      } catch (error) {
        console.error('Failed to load click count:', error);
      }
    };
    getClickCount();
  }, []);

  useEffect(() => {
    if (clickCount >= clickThreshold) {
      setType('support-us');
      setModalVisible(true);
      resetClickCount();
    }
  }, [clickCount]);

  useEffect(() => {
    if (credits?.amount <= 1) {
      setCanPlay(false);
    } else {
      setCanPlay(true);
    }
  }, [credits]);

  // Track when we have quiz data and mark as active question
  useEffect(() => {
    if (displayQuestionData && question && options && !isInitialized) {
      setHasActiveQuestion(true);
      setIsInitialized(true);
    }
  }, [displayQuestionData, question, options, isInitialized]);

  // Optimized focus effect to prevent navigation glitches
  useFocusEffect(
    React.useCallback(() => {
      let isMounted = true;

      const fetchLastNotification = async () => {
        try {
          if (!isMounted) return;

          const response = await Notifications.getLastNotificationResponseAsync();

          if (response && isMounted) {
            const notificationId = response.notification.request.identifier;
            const hasBeenProcessed = await AsyncStorage.getItem('lastNotificationId');

            if (notificationId !== hasBeenProcessed) {
              const data = response.notification.request.content.data;

              if (data.type === 'quiz' && data.questionData && isMounted) {
                setNavigatedQuestionData(data.questionData);
                setIsNavigatedQuestion(true);
                setHasActiveQuestion(true);
                await AsyncStorage.setItem('lastNotificationId', notificationId);
                return;
              }
            }
          }
        } catch (error) {
          console.error('Error handling last notification:', error);
        }

        // Only reset and fetch new question if there's no active question
        // This prevents the quiz from reloading when user navigates back to the screen
        if (isMounted && !hasActiveQuestion && !displayQuestionData) {
          setIsNavigatedQuestion(false);
          // Batch state updates to prevent multiple re-renders
          const resetState = () => {
            setOptionSelected(false);
            setIsCurrentSelectionCorrect(null);
            setSelectedOption(null);
            setGoToNext(false);
            setScore(0);
            setIsCorrect(null);
            setShowScoreMessage(false);
            setImage(null);
            setEnd(false);
            setGameOver(false);
            setModalVisible(false);
          };

          resetState();

          // Delay the API call slightly to prevent navigation glitches
          setTimeout(() => {
            if (isMounted) {
              getNextQuestion();
              refetch();
              setHasActiveQuestion(true);
            }
          }, 100);
        }
      };

      fetchLastNotification();

      return () => {
        isMounted = false;
      };
    }, [route.params?.questionData, hasActiveQuestion, displayQuestionData])
  );

  return (
    <QuizPresentation
      theme={theme}
      styles={styles}
      route={route}
      modalVisible={modalVisible}
      credits={credits}
      type={type}
      isFetching={isFetching}
      question={question}
      options={options}
      selectedOption={selectedOption}
      isCurrentSelectionCorrect={isCurrentSelectionCorrect}
      correctAnswer={correctAnswer}
      gameOver={gameOver}
      canClick={canClick}
      image={image}
      end={end}
      goToNext={goToNext}
      t={t}
      handleOptionSelect={handleOptionSelect}
      handleNextQuestion={handleNextQuestion}
      NavigateToCreditPage={NavigateToCreditPage}
      NavigateToCreditPagePurchase={NavigateToCreditPagePurchase}
      setModalVisible={setModalVisible}
      navigation={navigation}
    />
  );
};

// Presentational Component (pure UI)
const QuizPresentation = ({
  theme,
  styles,
  route,
  modalVisible,
  credits,
  type,
  isFetching,
  question,
  options,
  selectedOption,
  isCurrentSelectionCorrect,
  correctAnswer,
  gameOver,
  canClick,
  image,
  end,
  goToNext,
  t,
  handleOptionSelect,
  handleNextQuestion,
  NavigateToCreditPage,
  NavigateToCreditPagePurchase,
  setModalVisible,
  navigation,
}: QuizPresentationProps) => {
  const Loading = require('../../assets/loading.gif');

  return (
    <View style={styles.container}>
      {modalVisible && (
        <CustomModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          NavigateToCreditPage={NavigateToCreditPage}
          type={
            credits.amount <= 99
              ? 'credit'
              : type === 'support-us'
              ? type
              : 'new-credit'
          }
          handleConfirm={
            type === 'support-us'
              ? NavigateToCreditPagePurchase
              : NavigateToCreditPage
          }
          navigation={navigation}
        />
      )}

      {isFetching && (
        <View style={styles.loadingContainer}>
          {isFetching && (
            <Image source={Loading} style={{ width: rv(50), height: rv(50) }} />
          )}
        </View>
      )}

      {!isFetching && (
        <View style={styles.contentContainer}>
          <Text style={styles.questionText}>
            {question}
          </Text>
          <View style={styles.optionsContainer}>
            {options?.map((option, index) => (
              <OptionButton
                key={index}
                option={option}
                onPress={() => handleOptionSelect(option)}
                isSelected={selectedOption === option}
                isCorrect={isCurrentSelectionCorrect}
                correctAnswer={correctAnswer}
                gameOver={gameOver}
                canClick={canClick}
                style={{ width: '100%', marginBottom: wp('3'), fontFamily: 'regular' }}
              />
            ))}
          </View>
          <Animation image={image} end={end} />
        </View>
      )}

      {goToNext && (
        <TouchableOpacity
          style={styles.nextButton}
          onPress={handleNextQuestion}
        >
          <View style={styles.nextButtonContent}>
            <NextArrowSVG
              width={rv(35)}
              height={rv(30)}
              style={{ marginTop: rv(5) }}
              color="white"
            />
            <Text style={styles.nextButtonText}>
              {t("next")}
            </Text>
          </View>
        </TouchableOpacity>
      )}
    </View>
  );
};

// const styles = StyleSheet.create({
//   container: {
//     flex: 1, // Use flex in the parent container to cover the entire screen
//   },
//   centeredView: {
//     position: 'absolute', // Position the modal view absolutely
//     top: 0, // Align the top edge with the container
//     left: 0, // Align the left edge with the container
//     right: 0, // Align the right edge with the container
//     bottom: 0, // Align the bottom edge with the container
//     justifyContent: 'center',
//     alignItems: 'center',
//     backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent background
//   },
//   modalView: {
//     margin: 20,
//     backgroundColor: 'white',
//     borderRadius: 20,
//     padding: 35,
//     alignItems: 'center',
//     elevation: 5,
//   },
//   modalText: {
//     marginBottom: 15,
//     textAlign: 'center',
//   },
//   buttonContainer: {
//     flexDirection: 'row',
//     justifyContent: 'center',
//   },
// });

// Export container as default
export default QuizContainer;
