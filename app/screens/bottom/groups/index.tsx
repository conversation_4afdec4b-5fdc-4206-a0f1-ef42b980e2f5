// Create react native functional component

import React, { useContext, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Linking,
} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import SegmentedControlTab from 'react-native-segmented-control-tab';
import RecommendedGroupsScreen from './recommended-groups';
import MyGroups from './my-groups';
import { default as themeFont } from 'app/assets/themes/fonts.json';
import FloatingAddSVG from 'app/assets/svg/floating-add.svg';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useTranslation } from 'react-i18next';
import { useTheme, createThemedStyles } from 'app/theme';

export type GroupsScreenProps = {
  navigation?: any;
  route: any;
};

// Container Component
const GroupsScreenContainer: React.FC<GroupsScreenProps> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [tabIndex, setTabIndex] = useState(0);
  const { t } = useTranslation();

  function HandleTabChange(index: number) {
    setTabIndex(index);
  }

  return (
    <GroupsScreenPresentation
      theme={theme}
      styles={styles}
      tabIndex={tabIndex}
      onTabChange={HandleTabChange}
      navigation={navigation}
      t={t}
    />
  );
};

// Presentation Component
interface GroupsScreenPresentationProps {
  theme: any;
  styles: any;
  tabIndex: number;
  onTabChange: (index: number) => void;
  navigation: any;
  t: any;
}

const GroupsScreenPresentation: React.FC<GroupsScreenPresentationProps> = ({
  theme,
  styles,
  tabIndex,
  onTabChange,
  navigation,
  t,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        <View style={styles.tabWrapper}>
          <SegmentedControlTab
            activeTabStyle={styles.activeTab}
            activeTabTextStyle={styles.activeTabText}
            tabTextStyle={styles.tabText}
            tabStyle={styles.tab}
            values={[t('Groups_'), t('Groups_joinGroups')]}
            selectedIndex={tabIndex}
            onTabPress={onTabChange}
          />
        </View>
      </View>
      {tabIndex === 0 ? (
        <MyGroups navigation={navigation} />
      ) : tabIndex === 1 ? (
        <RecommendedGroupsScreen navigation={navigation} />
      ) : null}

      <TouchableOpacity
        activeOpacity={0.7}
        onPress={() => navigation.navigate('CreateGroup')}
        style={styles.floatingButton}
      >
        <FloatingAddSVG width={rv(45)} height={rv(45)} />
      </TouchableOpacity>
    </View>
  );
};

// Themed Styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  tabContainer: {
    alignItems: 'center',
  },
  tabWrapper: {
    width: '80%',
  },
  activeTab: {
    backgroundColor: theme.colors.background,
    borderBottomWidth: 2,
    borderBottomColor: theme.colors.accent,
  },
  activeTabText: {
    color: theme.colors.text,
    fontSize: rv(13),
    fontFamily: 'bold',
    paddingVertical: 8,
  },
  tabText: {
    color: theme.colors.text,
    fontSize: rv(13),
    paddingVertical: 4,
    fontFamily: 'regular',
  },
  tab: {
    backgroundColor: theme.colors.background,
    borderColor: theme.colors.background,
    borderBottomWidth: 0.5,
    borderBottomColor: theme.colors.textSecondary,
  },
  floatingButton: {
    position: 'absolute',
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    right: 30,
    bottom: 30,
  },
}));

export default GroupsScreenContainer;
