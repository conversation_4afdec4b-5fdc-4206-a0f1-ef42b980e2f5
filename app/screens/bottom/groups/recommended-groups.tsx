import React, { useCallback, useState } from 'react';
import { View, FlatList, Text, ListRenderItemInfo, Image } from 'react-native';
import Group from 'app/components/cards/group';
import { useSuggestedGroups } from 'app/redux/group/hooks';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import  BlankJoinGroupSVG  from 'app/assets/svg/empty-pages.svg';
import { useFocusEffect } from '@react-navigation/native';
import RecommendedTextSVG from '@/app/assets/svg/Recommendedgroups_text.svg'
import { useTheme, createThemedStyles } from 'app/theme';

export type RecommendedGroupsScreenProps = {
  navigation?: any;
};

const Loading = require('../../../assets/loading.gif');

// Container Component
const RecommendedGroupsScreenContainer: React.FC<RecommendedGroupsScreenProps> = ({
  navigation,
}) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const { status, data, error, isFetching, refetch, isLoading } =
    useSuggestedGroups();
  const [initialRefetch, setInitialRefetch] = useState(true)

  useFocusEffect(
    useCallback(() => {
      let isActive = true;
      const fetchData = async () => {
        try {
          if (isActive && initialRefetch) {
            refetch();
            setInitialRefetch(false);
          }
        } catch (e) {
          // Handle error
        }
      };

      fetchData();
      return () => {
        isActive = false;
      };
    }, [initialRefetch, refetch])
  );

  const handleRefresh = () => {
    setInitialRefetch(true);
    refetch();
  };

  return (
    <RecommendedGroupsScreenPresentation
      theme={theme}
      styles={styles}
      data={data}
      isLoading={isLoading}
      isFetching={isFetching}
      onRefresh={handleRefresh}
      navigation={navigation}
    />
  );
};

// Presentation Component
interface RecommendedGroupsScreenPresentationProps {
  theme: any;
  styles: any;
  data: any;
  isLoading: boolean;
  isFetching: boolean;
  onRefresh: () => void;
  navigation: any;
}

const RecommendedGroupsScreenPresentation: React.FC<RecommendedGroupsScreenPresentationProps> = ({
  theme,
  styles,
  data,
  isLoading,
  isFetching,
  onRefresh,
  navigation,
}) => {
  return (
    <View style={styles.container}>
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <Image source={Loading} style={styles.loadingImage} />
        </View>
      ) : (data && data.length > 0 ? (
        <FlatList
          refreshing={isFetching}
          onRefresh={onRefresh}
          style={styles.flatList}
          data={data}
          keyExtractor={(item: GroupProps) => item._id}
          renderItem={({ item }: ListRenderItemInfo<GroupProps>) => (
            <Group item={item} navigation={navigation} recommended={true} />
          )}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <BlankJoinGroupSVG style={styles.emptyIcon}/>
          <RecommendedTextSVG style={styles.emptyText} />
        </View>
      ))}
    </View>
  );
};

// Themed Styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingImage: {
    width: rv(50),
    height: rv(50),
  },
  flatList: {
    paddingHorizontal: 20,
  },
  emptyContainer: {
    alignContent: 'center',
    marginTop: '30%',
  },
  emptyIcon: {
    alignSelf: 'center',
  },
  emptyText: {
    alignSelf: 'center',
    marginTop: rv(20),
  },
}));

export default RecommendedGroupsScreenContainer;
