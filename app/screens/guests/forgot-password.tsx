/* eslint-disable prettier/prettier */
import React, { useState } from 'react';
import { useIsFocused } from '@react-navigation/native';
import {
  TextInput,
  View,
  Image,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView
} from 'react-native';
import { default as themeFont } from 'app/assets/themes/fonts.json';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { CustomButton, CustomText } from 'app/components/elements';
import SVG from 'app/providers/svg';
import { useSendResetPasswordToken } from 'app/redux/user/hooks';
import { useTranslation } from 'react-i18next';
import CustomModal from 'app/components/elements/Modals';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import Logo_onboard from '@/app/assets/svg/connectifyBigLogo.svg'
import Personal_info from '@/app/assets/svg/AgriFood Logo small.svg'

// Theme system imports
import { useTheme, createThemedStyles, ThemedStatusBar } from 'app/theme';
import { Theme } from 'app/types/theme';


type SignInScreenProps = {
  navigation: any;
};

// Container Component (handles logic, state, theme)
const ForgotPasswordContainer: React.FC<SignInScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const [email, setEmail] = useState('');

  const { mutate, isLoading } = useSendResetPasswordToken();
  const { t } = useTranslation();
  const [alertMessage, setAlertMessage] = useState('')
  const [modalVisible,setModalVisible] = useState(false)

  const handleSubmitPress = () => {
    if (!email) {
      setAlertMessage('Email is not allowed to empty');
      setModalVisible(true)
      return;
    }

    mutate({
      email,
    });
  };

  const handleConfirm = async () => {
    setModalVisible(false)
  };

  return (
    <ForgotPasswordPresentation
      theme={theme}
      styles={styles}
      navigation={navigation}
      email={email}
      setEmail={setEmail}
      isLoading={isLoading}
      t={t}
      alertMessage={alertMessage}
      modalVisible={modalVisible}
      setModalVisible={setModalVisible}
      handleSubmitPress={handleSubmitPress}
      handleConfirm={handleConfirm}
    />
  );
};

// Presentational Component Props Interface
interface ForgotPasswordPresentationProps {
  theme: Theme;
  styles: any;
  navigation: any;
  email: string;
  setEmail: (email: string) => void;
  isLoading: boolean;
  t: any;
  alertMessage: string;
  modalVisible: boolean;
  setModalVisible: (visible: boolean) => void;
  handleSubmitPress: () => void;
  handleConfirm: () => void;
}

// Presentational Component (pure UI rendering)
const ForgotPasswordPresentation: React.FC<ForgotPasswordPresentationProps> = ({
  theme,
  styles,
  navigation,
  email,
  setEmail,
  isLoading,
  t,
  alertMessage,
  modalVisible,
  setModalVisible,
  handleSubmitPress,
  handleConfirm,
}) => {
  return (
    <ScrollView contentContainerStyle={styles.scrollContainer}>
      {modalVisible && (
      <CustomModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          type="alert" // or "stake" based on the scenario
          message={alertMessage}
          handleConfirm={handleConfirm}
          navigation
        />)}
      <SafeAreaView>
      <ThemedStatusBar />

      <View>
        <View style={styles.mainContainer}>

          <View style={styles.contentContainer}>


            <View>
             <Logo_onboard />

            </View>

            <View style={styles.formWrapper}>
            <View style={styles.headerTextContainer}>
                <CustomText style={styles.titleText} textType='semi-bold'>
                  Forgot Password?
                </CustomText>
                <CustomText style={styles.subtitleText} textType='semi-bold'>
                  Don`t worry we got you covered
                </CustomText>
              </View>
              <View style={styles.inputRow}>
                <View style={styles.inputContainer}>
                     <CustomText style={styles.fieldLabel} textType='semi-bold'>
                  Email:
                </CustomText>
                  <TextInput
                    style={styles.emailInput}
                    onChangeText={(text: any) => {
                      setEmail(text);
                    }}
                    placeholder=''
                    value={email}
                    placeholderTextColor={theme.colors.textPlaceholder}
                  />
                </View>
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <CustomButton
                label={t('ForgotPin_btn')}
                onPress={() => {
                  handleSubmitPress();
                }}
                buttonTheme='primary'
                style={{

                }}
                loading={isLoading}
              />
            </View>
            <View style={styles.bottomLinksContainer}>
            <View >
      <TouchableOpacity
        onPress={() => {
          navigation.navigate('register');
        }}
      >
        <CustomText style={styles.linkText}>
          {t('LoginPage_signUp')}
        </CustomText>
      </TouchableOpacity>
    </View>
    <CustomText
      textType="bold"
      style={styles.dividerText}
    >
      |
    </CustomText>
    <View>
      <TouchableOpacity
        onPress={() => {
          navigation.navigate('login');
        }}
      >
        <CustomText style={styles.linkText} textType="medium">
          Login
        </CustomText>
      </TouchableOpacity>
    </View>
            </View>


</View>


        </View>
      </View>
      </SafeAreaView>
    </ScrollView>
  );
};

// Create themed styles
const createStyles = createThemedStyles((theme) => ({
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    paddingHorizontal: wp('5')
  },
  mainContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    display: 'flex',
    alignItems: 'center',
  },
  formWrapper: {
    width: 380
  },
  headerTextContainer: {
    marginTop: 5,
    marginBottom: 40,
    paddingTop: rv(20)
  },
  titleText: {
    fontSize: rv(13),
    color: theme.colors.textSecondary,
  },
  subtitleText: {
    fontSize: rv(13),
    color: theme.colors.textSecondary,
  },
  inputRow: {
    flex: 1,
    flexDirection: 'row',
  },
  inputContainer: {
    flex: 1,
  },
  fieldLabel: {
    fontSize: rv(13),
    color: theme.colors.textSecondary,
  },
  emailInput: {
    height: 60,
    width: '100%',
    paddingLeft: 20,
    backgroundColor: theme.colors.inputBackground,
    marginBottom: 15,
    borderStyle: 'solid',
    marginTop: 10,
    fontSize: rv(12),
    fontFamily: 'regular',
    borderWidth: 1.5,
    borderColor: theme.colors.inputBorder,
    color: theme.colors.text
  },
  buttonContainer: {
    width: wp('90%'),
    alignItems: 'center',
    display: 'flex'
  },
  bottomLinksContainer: {
    width: wp('90%'),
    flexDirection: 'row',
    marginTop: rv(20),
    alignItems: 'center',
    justifyContent: 'center'
  },
  linkText: {
    color: theme.colors.text,
    fontSize: rv(13),
    textAlign: 'left',
    fontFamily: 'medium'
  },
  dividerText: {
    paddingHorizontal: 20,
    color: theme.colors.primary,
  }
}));

// Export container as default
export default ForgotPasswordContainer;
