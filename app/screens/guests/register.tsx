import React, {
  FunctionComponent,
  useState,
  useEffect,
  useContext,
} from "react";
import {
  View,
  Image,
  Text,
  TextInput,
  ScrollView,
  SafeAreaView,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
} from "react-native";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import { SignUpHeaderSVG } from "app/providers/svg/loader";
import { CustomText, CustomButton } from "app/components/elements";
import RNPickerSelect from "react-native-picker-select";
import { Flag } from "react-native-svg-flagkit";
import countryAndCode from "app/Data/countryAndCode";
import { CommonStyles, PickerSelectStyle } from "app/assets/styles";
import Loader from "app/components/elements/Loader";
import { accountRegister } from "app/redux/user/hooks";
import { useSelector } from "react-redux";
import { appLoading } from "../../redux/main/reducer";
import { Country as CountryType } from "../../types/types";
import CountryPicker, { Country } from "react-native-country-picker-modal";
import { useTranslation } from "react-i18next";
import CustomModal from "app/components/elements/Modals";
import { clearDeviceToken, setLoading } from "app/redux/main/reducer";
import { responsiveValue as rv } from "app/providers/responsive-value";
import Personal_info from "@/app/assets/svg/connectifyHorizontallogo.svg";

// Theme system imports
import { useTheme, createThemedStyles, ThemedStatusBar } from "app/theme";
import { createCommonStyles } from "app/assets/styles/CommonStyles";
import { Theme } from "app/types/theme";
interface CustomCountryPickerProps {
  onCountrySelect: (countryCode: string) => void;
}
// Container Component (handles logic, state, theme)
const RegisterContainer: FunctionComponent<{
  navigation: any;
}> = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const commonStyles = createCommonStyles(theme);

  // const loading = useSelector(appLoading);
  const [loading, setLoading] = useState(false);

  const [phone, setPhone] = useState("");

  const [password, setPassword] = useState("");
  const [countryFlag, setCountryFlag] = useState("NG");
  const [selectedCountry, setSelectedCountry] = useState<CountryType | null>(
    null
  );
  const [countryCode, setCountryCode] = useState("+234");
  let [firstName, setFirstName] = useState("");
  let [lastName, setLastName] = useState("");
  let [email, setEmail] = useState("");
  const { t } = useTranslation();
  const [alertMessage, setAlertMessage] = useState("");
  const [modalVisible, setModalVisible] = useState(false);
  const [isCountryPickerVisible, setCountryPickerVisible] = useState(false);
  const [eulaAccepted, setEulaAccepted] = useState(false);

  type CountryProps = {
    label: string;
    value: string;
    name: string;
    flag: string;
  };

  function getCountryFlag(): string {
    let countryData = countryAndCode.find((item: CountryProps) => {
      return item.value == countryCode;
    });
    if (countryData) {
      return countryData.flag;
    } else {
      return "";
    }
  }

  function getCountryName(): string {
    let countryData = countryAndCode.find((item: CountryProps) => {
      return item.value == countryCode;
    });
    if (countryData) {
      return countryData.name;
    } else {
      return "";
    }
  }

  const handleCountryChange = (country: Country) => {
    setSelectedCountry(country);
    setCountryCode(`+${country.callingCode[0]}`);
  };

  const signUp = async () => {
    setLoading(false);
    if (!firstName) {
      setAlertMessage("Please enter your first name");
      setModalVisible(true);
      return;
    } else if (firstName.trim().length < 2) {
      setAlertMessage("Length of first name should be more than 2 characters");
      setModalVisible(true);
      return;
    }

    // Last name validation
    if (!lastName) {
      setAlertMessage("Please enter your last name");
      setModalVisible(true);
      return;
    } else if (lastName.trim().length < 2) {
      setAlertMessage("Length of last name should be more than 2 characters");
      setModalVisible(true);
      return;
    }
    if (!email) {
      setAlertMessage("Please enter your email address");
      setModalVisible(true); // Show the modal
      return;
    }
    if (!password) {
      setAlertMessage("You need to set a pin for your account");
      setModalVisible(true); // Show the modal
      return;
    }
    if (!phone) {
      setAlertMessage("Please enter your phone number");
      setModalVisible(true); // Show the modal
      return;
    }
    if (!validateEmail(email)) {
      setAlertMessage("Invalid email addresss");
      setModalVisible(true); // Show the modal
      return;
    }
    if (!countryCode) {
      setAlertMessage("Please select a countrycode");
      setModalVisible(true); // Show the modal
      return;
    }
    if (!eulaAccepted) {
      setAlertMessage(
        "You must agree to the End-User License Agreement and Privacy Policy to continue"
      );
      setModalVisible(true);
      return;
    }

    const formData = {
      first_name: firstName,
      last_name: lastName,
      email,
      phone_number: {
        code: countryCode,
        number: phone,
      },
      country: getCountryName(),
      password: password,
    };
    setLoading(true);

    try {
      // Call the accountRegister function
      await accountRegister(formData);
      navigation.navigate("verify-email");
    } catch (error) {
      // Handle error (e.g., show an alert or error message)
      console.error("Registration error:", error);
    } finally {
      // Always reset the loading state to false
      setLoading(false);
    }
  };

  const validateEmail = (email: string): boolean => {
    var re =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(email);
  };

  useEffect(() => {
    setCountryFlag(getCountryFlag());
  }, [countryCode]);

  const handleConfirm = async () => {
    setModalVisible(false);
  };

  return (
    <RegisterPresentation
      theme={theme}
      styles={styles}
      commonStyles={commonStyles}
      navigation={navigation}
      loading={loading}
      phone={phone}
      setPhone={setPhone}
      password={password}
      setPassword={setPassword}
      countryFlag={countryFlag}
      selectedCountry={selectedCountry}
      countryCode={countryCode}
      firstName={firstName}
      setFirstName={setFirstName}
      lastName={lastName}
      setLastName={setLastName}
      email={email}
      setEmail={setEmail}
      t={t}
      alertMessage={alertMessage}
      modalVisible={modalVisible}
      setModalVisible={setModalVisible}
      isCountryPickerVisible={isCountryPickerVisible}
      setCountryPickerVisible={setCountryPickerVisible}
      eulaAccepted={eulaAccepted}
      setEulaAccepted={setEulaAccepted}
      handleCountryChange={handleCountryChange}
      signUp={signUp}
      handleConfirm={handleConfirm}
    />
  );
};
// Presentational Component Props Interface
interface RegisterPresentationProps {
  theme: Theme;
  styles: any;
  commonStyles: any;
  navigation: any;
  loading: boolean;
  phone: string;
  setPhone: (phone: string) => void;
  password: string;
  setPassword: (password: string) => void;
  countryFlag: string;
  selectedCountry: CountryType | null;
  countryCode: string;
  firstName: string;
  setFirstName: (name: string) => void;
  lastName: string;
  setLastName: (name: string) => void;
  email: string;
  setEmail: (email: string) => void;
  t: any;
  alertMessage: string;
  modalVisible: boolean;
  setModalVisible: (visible: boolean) => void;
  isCountryPickerVisible: boolean;
  setCountryPickerVisible: (visible: boolean) => void;
  eulaAccepted: boolean;
  setEulaAccepted: (accepted: boolean) => void;
  handleCountryChange: (country: Country) => void;
  signUp: () => void;
  handleConfirm: () => void;
}

// Presentational Component (pure UI rendering)
const RegisterPresentation: React.FC<RegisterPresentationProps> = ({
  theme,
  styles,
  commonStyles,
  navigation,
  loading,
  phone,
  setPhone,
  password,
  setPassword,
  countryFlag,
  selectedCountry,
  countryCode,
  firstName,
  setFirstName,
  lastName,
  setLastName,
  email,
  setEmail,
  t,
  alertMessage,
  modalVisible,
  setModalVisible,
  isCountryPickerVisible,
  setCountryPickerVisible,
  eulaAccepted,
  setEulaAccepted,
  handleCountryChange,
  signUp,
  handleConfirm,
}) => {
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {modalVisible && (
          <CustomModal
            modalVisible={modalVisible}
            setModalVisible={setModalVisible}
            type="alert" // or "stake" based on the scenario
            message={alertMessage}
            handleConfirm={handleConfirm}
            navigation
          />
        )}
        <ThemedStatusBar />
        <Loader loading={loading} />

        <View style={styles.headerContainer}>
          <View style={styles.headerTextContainer}>
            <CustomText style={styles.headerTitle}>
              {t("Signup_OpeningText")}
            </CustomText>
          </View>
          <Personal_info style={{ alignSelf: "flex-end" }} />
        </View>
        <View style={styles.formContainer}>
          <CustomText style={styles.subtitleText}>
            {t("Signup_page1text")}
          </CustomText>

          <View style={styles.phoneFieldContainer}>
            <CustomText style={styles.fieldLabel}>
              {t("LoginPage_PhoneNumber")}
            </CustomText>

            <View style={styles.phoneInputRow}>
              <View style={styles.countryCodeContainer}>
                {/* <Flag id={countryFlag} width={25} height={25} /> */}
                <TouchableOpacity
                  onPress={() => {
                    setCountryPickerVisible(true);
                  }}
                  hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
                  style={styles.arrowContainer}
                >
                  <Text style={styles.arrow}>▼</Text>
                </TouchableOpacity>
                <View style={styles.container2}>
                  <Text style={styles.countryCodeText}>
                    +{selectedCountry ? selectedCountry.callingCode[0] : "234"}
                  </Text>
                  {selectedCountry && (
                    <Image
                      style={styles.flagImage}
                      source={{ uri: selectedCountry.flag }}
                    />
                  )}
                  <CountryPicker
                    withCallingCode
                    withFilter
                    withFlag
                    onSelect={handleCountryChange}
                    countryCode={selectedCountry ? selectedCountry.cca2 : "NG"}
                    containerButtonStyle={styles.countryPickerContainer}
                    visible={isCountryPickerVisible}
                    onClose={() => setCountryPickerVisible(false)}
                  />
                </View>
              </View>
              <View style={styles.phoneNumberInputContainer}>
                <TextInput
                  style={commonStyles.inputField}
                  maxLength={10}
                  onChangeText={(text: string) => {
                    setPhone(text);
                  }}
                  inlineImageLeft="callplain"
                  placeholder={t("Signup_phoneNumber")}
                  inlineImagePadding={20}
                  keyboardType="phone-pad"
                  value={phone}
                  placeholderTextColor={theme.colors.textPlaceholder}
                />
              </View>
            </View>
          </View>

          <View style={styles.fieldContainer}>
            <CustomText style={styles.fieldLabel}>{t("firstName")}</CustomText>

            <TextInput
              style={commonStyles.inputField}
              onChangeText={(text: string) => {
                setFirstName(text);
              }}
              inlineImageLeft="callplain"
              // placeholder={t("Signup_firstName")}
              inlineImagePadding={20}
              value={firstName}
              placeholderTextColor={theme.colors.textPlaceholder}
            />
          </View>

          <View style={styles.fieldContainer}>
            <CustomText style={styles.fieldLabel}>{t("lastName")}</CustomText>

            <TextInput
              style={commonStyles.inputField}
              onChangeText={(text: string) => {
                setLastName(text);
              }}
              inlineImageLeft="callplain"
              // placeholder={t("Signup_lastName")}
              inlineImagePadding={20}
              value={lastName}
              placeholderTextColor={theme.colors.textPlaceholder}
            />
          </View>

          <View style={styles.fieldContainer}>
            <CustomText style={styles.fieldLabel}>
              {t("Signup_email")}
            </CustomText>

            <TextInput
              style={commonStyles.inputField}
              onChangeText={(text: string) => {
                setEmail(text);
              }}
              inlineImageLeft="callplain"
              // placeholder='Email'
              inlineImagePadding={20}
              value={email}
              placeholderTextColor={theme.colors.textPlaceholder}
            />
          </View>

          <View style={styles.fieldContainer}>
            <CustomText style={styles.fieldLabel}>{t("Signup_pin")}</CustomText>

            <TextInput
              style={commonStyles.inputField}
              onChangeText={(text) => setPassword(text)}
              inlineImageLeft="key"
              // placeholder={t('LoginPage_pin')}
              inlineImagePadding={20}
              textContentType="password"
              secureTextEntry={true}
              keyboardType="numeric"
              maxLength={4}
              value={password}
              placeholderTextColor={theme.colors.textPlaceholder}
            />
          </View>
        </View>

        <View style={styles.eulaContainer}>
          <TouchableOpacity
            onPress={() => setEulaAccepted(!eulaAccepted)}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }} // Increased touchable area using hitSlop
            style={styles.checkbox}
          >
            {eulaAccepted && <View style={styles.checkboxFill} />}
          </TouchableOpacity>
          <View style={styles.eulaTextContainer}>
            <CustomText style={styles.eulaText}>
              {t("I agree to the ")}
            </CustomText>
            <CustomText
              style={styles.eulaLink}
              onPress={() => navigation.navigate("eula-screen")}
            >
              {t("End-User License Agreement")}
            </CustomText>
            <CustomText style={styles.eulaText}>{t(" and ")}</CustomText>
            <CustomText
              style={styles.eulaLinkBold}
              onPress={() => navigation.navigate("privacy-policy-screen")}
            >
              {t("Privacy Policy")}
            </CustomText>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <CustomButton
            label={t("Signup_nextbtn")}
            onPress={() => signUp()}
            loading={loading}
            style={{
              width: "100%",
              maxWidth: rv(118),
              borderRadius: rv(12),
              marginTop: rv(22),
              alignSelf: "flex-end",
            }}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

// Create themed styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    // marginTop: rv(10),
    flexGrow: 1,
    backgroundColor: theme.colors.background,
  },
  scrollContainer: {
    flexGrow: 1,
    backgroundColor: theme.colors.background,
    paddingHorizontal: wp("5"),
    paddingTop: rv(10),
    paddingBottom: rv(10),
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: wp("2"),
  },
  headerTextContainer: {
    width: "50%",
  },
  headerTitle: {
    color: theme.colors.textSecondary,
    fontSize: rv(21),
    fontFamily: "medium",
  },
  formContainer: {
    flexDirection: "column",
  },
  subtitleText: {
    fontSize: rv(13),
    marginTop: rv(5),
    color: theme.colors.textSecondary,
    fontFamily: "medium",
  },
  phoneFieldContainer: {
    marginTop: rv(25),
    flexDirection: "column",
  },
  fieldContainer: {
    flexDirection: "column",
  },
  fieldLabel: {
    color: theme.colors.text,
    fontSize: rv(12),
    fontFamily: "medium",
  },
  phoneInputRow: {
    flexDirection: "row",
  },
  countryCodeContainer: {
    ...{
      height: rv(50),
      paddingHorizontal: 20,
      backgroundColor: theme.colors.inputBackground,
      marginBottom: 15,
      borderStyle: "solid",
      marginTop: 10,
      fontSize: rv(13),
      fontFamily: "medium",
      borderWidth: 2,
      borderColor: theme.colors.inputBorder,
      color: theme.colors.textPlaceholder,
    },
    padding: 5,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 10,
    flexDirection: "row",
  },
  phoneNumberInputContainer: {
    flex: 1,
    marginLeft: 1,
  },
  container2: {
    flexDirection: "row",
    alignItems: "center",
  },
  countryCodeText: {
    marginLeft: rv(5),
    fontSize: 14,
    color: theme.colors.text,
  },
  flagImage: {
    width: rv(20),
    height: rv(10),
    marginLeft: -20,
  },
  countryPickerContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  arrowContainer: {
    marginLeft: 5,
  },
  arrow: {
    fontSize: rv(14),
    color: theme.colors.text,
  },
  eulaContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: rv(20),
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  checkboxFill: {
    width: 12,
    height: 12,
    backgroundColor: theme.colors.primary,
  },
  eulaTextContainer: {
    flexDirection: "row",
    alignItems: "center",
    flexWrap: "wrap",
  },
  eulaText: {
    fontSize: rv(12),
    color: theme.colors.textSecondary,
    fontFamily: "regular",
  },
  eulaLink: {
    fontSize: rv(12),
    color: theme.colors.primary,
    fontFamily: "regular",
  },
  eulaLinkBold: {
    fontSize: rv(12),
    color: theme.colors.primary,
    fontFamily: "medium",
  },
  buttonContainer: {
    alignItems: "flex-end",
  },
}));

// Export container as default
export default RegisterContainer;
