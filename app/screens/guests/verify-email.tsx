import React, {
  FunctionComponent,
  useState,
  useEffect,
} from "react";
import {
  View,
  Image,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import {
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import { SignUpHeaderSVG } from "app/providers/svg/loader";
import { CustomText, CustomButton } from "app/components/elements";
import Loader from "app/components/elements/Loader";
// import { OTPInput } from 'app/components/elements/otp-input';

import { OtpInput } from "react-native-otp-entry";
import {
  useSendEmailVerificationCode,
  useValidateEmailVerificationCode,
} from "app/redux/user/hooks";
import { useSelector } from "react-redux";
import { userAuthInfo } from "../../redux/user/reducer";
import { useTheme, createOptimizedThemedStyles } from "app/theme";
import { Theme } from "app/types/theme";
import { useOptimizedFontSize } from "app/hooks/useOptimizedFontSize";

// Container Component
const VerifyEmailContainer: FunctionComponent<{
  navigation: any;
}> = ({ navigation }) => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  const [codeSent, setCodeSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [otp, setOtp] = useState("");

  const { mutateAsync: sendOTP, isLoading: loadingSentCode } =
    useSendEmailVerificationCode();
  const { mutateAsync: verifyToken, isLoading: loadingVerifyCode } =
    useValidateEmailVerificationCode();
  const user = useSelector(userAuthInfo);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    if (user && user.verified) {
      navigation.replace("Onboarding");
    } else {
      setIsReady(true);
    }
  }, [user]);

  useEffect(() => {
    const timer = setInterval(() => {
      if (countdown > 0) {
        setCountdown(countdown - 1);
      }
    }, 1000);
    return () => clearInterval(timer);
  }, [countdown]);

  function SendOTP() {
    sendOTP()
      .then((response) => {
        setCodeSent(true);
        setCountdown(response.secLeft);
      })
      .catch((error) => {
        if (error.message == "Email has already been verified") {
          // navigation.replace("Onboarding");
        }
      });
  }

  function validateOTP() {
    if (otp && otp.length === 4) {
      verifyToken({ token: otp }).then(() => {
        navigation.navigate("Onboarding");
      });
    }
  }

  useEffect(() => {
    validateOTP();
  }, [otp]);

  if (!isReady) {
    return null;
  }

  return (
    <VerifyEmailPresentation
      theme={theme}
      styles={styles}
      loadingSentCode={loadingSentCode}
      loadingVerifyCode={loadingVerifyCode}
      codeSent={codeSent}
      countdown={countdown}
      SendOTP={SendOTP}
      validateOTP={validateOTP}
      user={user}
      setOtp={setOtp}
    />
  );
};

// Presentational Component
interface VerifyEmailPresentationProps {
  theme: Theme;
  styles: any;
  loadingSentCode: boolean;
  loadingVerifyCode: boolean;
  codeSent: boolean;
  countdown: number;
  SendOTP: () => void;
  validateOTP: () => void;
  user: any;
  setOtp: (value: string) => void;
}

const VerifyEmailPresentation: React.FC<VerifyEmailPresentationProps> = ({
  styles,
  loadingSentCode,
  countdown,
  SendOTP,
  validateOTP,
  user,
  setOtp,
}) => {
  return (
    <ScrollView style={styles.scrollView}>
      <Loader loading={loadingSentCode} />

      <View style={styles.logoContainer}>
        <Image source={require("app/assets/logo.png")} style={styles.logo} />

        <View style={styles.headerImageContainer}>
          <SignUpHeaderSVG />
        </View>
      </View>

      <View style={styles.contentContainer}>
        <View style={styles.titleContainer}>
          <CustomText style={styles.titleText}>Email Verification</CustomText>
        </View>

        <CustomText style={styles.otpDescriptionText}>
          Enter 4-digit code sent to your email
          {user?.email && (
            <CustomText style={styles.emailText}>{`${user.email}`}</CustomText>
          )}
        </CustomText>

        <OtpInput
          numberOfDigits={4}
          focusColor="green"
          autoFocus={false}
          hideStick={true}
          placeholder="****"
          blurOnFilled={true}
          disabled={false}
          type="numeric"
          secureTextEntry={false}
          focusStickBlinkingDuration={500}
          onFocus={() => console.log("Focused")}
          onBlur={() => console.log("Blurred")}
          onTextChange={(text: string) => setOtp(text)}
          onFilled={(text: string) => setOtp(text)}
          textInputProps={{
            accessibilityLabel: "One-Time Password",
          }}
          textProps={{
            accessibilityRole: "text",
            accessibilityLabel: "OTP digit",
            allowFontScaling: false,
          }}
          theme={{
            containerStyle: styles.otpContainer,
            pinCodeContainerStyle: styles.pinCodeContainer,
            pinCodeTextStyle: styles.pinCodeText,
            focusStickStyle: styles.focusStick,
            focusedPinCodeContainerStyle: styles.activePinCodeContainer,
            placeholderTextStyle: styles.placeholderText,
            filledPinCodeContainerStyle: styles.filledPinCodeContainer,
            disabledPinCodeContainerStyle: styles.disabledPinCodeContainer,
          }}
        />

        <View style={{ width: wp("90%"), marginTop: 10 }}>
          <CustomButton
            label={`Validate OTP`}
            onPress={() => {
              validateOTP();
            }}
            buttonTheme="primary"
            style={{
              borderRadius: 14,
            }}
            loading={loadingSentCode}
          />
        </View>
        <View style={styles.resendContainer}>
          <CustomText style={styles.resendText}>
            Didn't receive code?
          </CustomText>
          <TouchableOpacity
            onPress={() => countdown === 0 && SendOTP()}
            disabled={countdown > 0}
          >
            <CustomText style={styles.resendButtonText}>
              Resend OTP {countdown > 0 && `(${countdown}s)`}
            </CustomText>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

// Create themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  scrollView: {
    flex: 1,
    backgroundColor: theme.colors.background,
    flexDirection: "column",
    paddingHorizontal: wp("5"),
  },
  logoContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  logo: {
    width: 200,
    height: 50,
  },
  headerImageContainer: {
    width: "50%",
  },
  contentContainer: {
    flexDirection: "column",
    marginTop: 30,
  },
  titleContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  titleText: {
    color: theme.colors.textSecondary,
    fontSize: fs("XXXL"),
    fontFamily: "medium",
    marginTop: 50,
  },
  resendContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 20,
  },
  resendText: {
    fontSize: fs("BASE"),
    color: theme.colors.textSecondary,
    fontFamily: "medium",
  },
  resendButton: {
    marginLeft: 5,
  },
  resendButtonText: {
    fontSize: fs("BASE"),
    color: theme.colors.textSecondary,
    fontFamily: "bold",
  },
  countdownText: {
    alignSelf: "center",
    color: theme.colors.textSecondary,
    fontFamily: "medium",
  },
  buttonContainer: {
    width: wp("90%"),
    marginTop: 30,
    flexDirection: "column",
  },
  verifyButton: {
    borderRadius: 14,
    marginTop: 0,
  },
  logoutButton: {
    alignSelf: "center",
    marginLeft: 5,
    marginTop: 15,
  },
  logoutButtonText: {
    fontSize: fs("BASE"),
    color: theme.colors.error,
    fontFamily: "bold",
  },
  pinCodeText: {
    fontSize: fs("BASE"),
    color: theme.colors.textSecondary,
    fontFamily: "medium",
  },
  placeholderText: {
    fontSize: fs("BASE"),
    color: theme.colors.textSecondary,
    fontFamily: "medium",
  },
  otpDescriptionText: {
    marginVertical: 10,
    fontSize: fs("BASE"),
    color: theme.colors.textSecondary,
    fontFamily: "medium",
  },
  otpContainer: {
    width: wp("80%"),
    alignSelf: "center",
    marginVertical: 10,
  },
  emailText: {
    color: theme.colors.buttonPrimary,
    fontFamily: "bold",
    fontSize: fs("BASE"),
    textDecorationLine: "underline",
  },
}));

export default VerifyEmailContainer;
