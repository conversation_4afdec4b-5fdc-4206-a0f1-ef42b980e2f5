import React, {
  FunctionComponent,
  useState,
  useRef,
  useEffect,
  useContext,
} from "react";
import {
  View,
  Image,
  TextInput,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import { SignUpHeaderSVG } from "app/providers/svg/loader";
import { CustomText, CustomButton } from "app/components/elements";
import Loader from "app/components/elements/Loader";
// import { OTPInput } from 'app/components/elements/otp-input';

import { OtpInput } from "react-native-otp-entry";
import {
  useSendPhoneVerificationCode,
  useValidatePhoneCode,
} from "app/redux/user/hooks";
import { useSelector } from "react-redux";
import { userAuthInfo } from "../../redux/user/reducer";
import { responsiveValue as rv } from "app/providers/responsive-value"; // import Select from 'app/components/elements/select';
// import { toHHMMSS } from 'app/helpers';
import { useTheme, createThemedStyles } from "app/theme";
import { Theme } from "app/types/theme";

// Container Component
const VerifyEmailContainer: FunctionComponent<{
  navigation: any;
}> = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [codeSent, setCodeSent] = useState(false);
  const [type, setType] = useState("sms");
  const [countdown, setCountdown] = useState(0);
  const [otp, setOtp] = useState("");

  const { mutateAsync: sendOTP, isLoading: loadingSentCode } =
    useSendPhoneVerificationCode();
  const { mutateAsync: verifyToken, isLoading: loadingVerifyCode } =
    useValidatePhoneCode();
  const user = useSelector(userAuthInfo);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    if (user && user.verified) {
      navigation.replace("Onboarding");
    } else {
      setIsReady(true);
    }
  }, [user]);

  useEffect(() => {
    const timer = setInterval(() => {
      if (countdown > 0) {
        setCountdown(countdown - 1);
      }
    }, 1000);
    return () => clearInterval(timer);
  }, [countdown]);

  function SendOTP() {
    let payload = {
      type,
    };
    sendOTP(payload)
      .then((response) => {
        setCodeSent(true);
        setCountdown(response.secLeft);
      })
      .catch((error) => {
        if (error.message == "Email has already been verified") {
          // navigation.replace("Onboarding");
        }
      });
  }

  function validateOTP() {
    if (otp && otp.length === 4) {
      verifyToken({ token: otp }).then((response) => {
        navigation.navigate("Onboarding");
      });
    }
  }

  useEffect(() => {
    validateOTP();
  }, [otp]);

  const data = [
    { label: "Whatsapp", value: "whatsapp" },
    { label: "SMS", value: "sms" },
  ];

  if (!isReady) {
    return null;
  }

  return (
    <VerifyEmailPresentation
      theme={theme}
      styles={styles}
      loadingSentCode={loadingSentCode}
      loadingVerifyCode={loadingVerifyCode}
      codeSent={codeSent}
      countdown={countdown}
      data={data}
      SendOTP={SendOTP}
      validateOTP={validateOTP}
      user={user}
    />
  );
};

// Presentational Component
interface VerifyEmailPresentationProps {
  theme: Theme;
  styles: any;
  loadingSentCode: boolean;
  loadingVerifyCode: boolean;
  codeSent: boolean;
  countdown: number;
  data: any[];
  SendOTP: () => void;
  validateOTP: () => void;
  user: any;
}

const VerifyEmailPresentation: React.FC<VerifyEmailPresentationProps> = ({
  theme,
  styles,
  loadingSentCode,
  loadingVerifyCode,
  codeSent,
  countdown,
  data,
  SendOTP,
  validateOTP,
  user,
}) => {
  return (
    <ScrollView style={styles.scrollView}>
      <Loader loading={loadingSentCode} />

      <View style={styles.logoContainer}>
        <Image source={require("app/assets/logo.png")} style={styles.logo} />

        <View style={styles.headerImageContainer}>
          <SignUpHeaderSVG />
        </View>
      </View>

      <View style={styles.contentContainer}>
        <View style={styles.titleContainer}>
          <CustomText style={styles.titleText}>Email Verification</CustomText>
        </View>

        <CustomText style={styles.otpDescriptionText}>
          Enter 4-digit code sent to your email
          {user?.email && (
            <CustomText style={styles.emailText}>{`${user.email}`}</CustomText>
          )}
        </CustomText>

        <OtpInput
          numberOfDigits={4}
          focusColor="green"
          autoFocus={false}
          hideStick={true}
          placeholder="****"
          blurOnFilled={true}
          disabled={false}
          type="numeric"
          secureTextEntry={false}
          focusStickBlinkingDuration={500}
          onFocus={() => console.log("Focused")}
          onBlur={() => console.log("Blurred")}
          onTextChange={(text) => console.log(text)}
          onFilled={(text) => console.log(`OTP is ${text}`)}
          textInputProps={{
            accessibilityLabel: "One-Time Password",
          }}
          textProps={{
            accessibilityRole: "text",
            accessibilityLabel: "OTP digit",
            allowFontScaling: false,
          }}
          theme={{
            containerStyle: styles.otpContainer,
            pinCodeContainerStyle: styles.pinCodeContainer,
            pinCodeTextStyle: styles.pinCodeText,
            focusStickStyle: styles.focusStick,
            focusedPinCodeContainerStyle: styles.activePinCodeContainer,
            placeholderTextStyle: styles.placeholderText,
            filledPinCodeContainerStyle: styles.filledPinCodeContainer,
            disabledPinCodeContainerStyle: styles.disabledPinCodeContainer,
          }}
        />

        <View style={{ width: wp("90%"), marginTop: 10 }}>
          <CustomButton
            label={`Validate OTP`}
            onPress={() => {
              validateOTP();
            }}
            buttonTheme="primary"
            style={{
              borderRadius: 14,
            }}
            loading={loadingSentCode}
          />
        </View>
        <View style={styles.resendContainer}>
          <CustomText style={styles.resendText}>
            Didn't receive code?
          </CustomText>
          <TouchableOpacity
            onPress={() => countdown === 0 && SendOTP()}
            disabled={countdown > 0}
          >
            <CustomText style={styles.resendButtonText}>
              Resend OTP {countdown > 0 && `(${countdown}s)`}
            </CustomText>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

// Create themed styles
const createStyles = createThemedStyles((theme) => ({
  scrollView: {
    flex: 1,
    backgroundColor: theme.colors.background,
    flexDirection: "column",
    paddingHorizontal: wp("5"),
  },
  logoContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  logo: {
    width: 200,
    height: 50,
  },
  headerImageContainer: {
    width: "50%",
  },
  contentContainer: {
    flexDirection: "column",
    marginTop: 30,
  },
  titleContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  titleText: {
    color: theme.colors.textSecondary,
    fontSize: 21,
    fontFamily: "medium",
    marginTop: 50,
  },
  resendContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 20,
  },
  resendText: {
    fontSize: rv(12),
    color: theme.colors.textSecondary,
    fontFamily: "medium",
  },
  resendButton: {
    marginLeft: 5,
  },
  resendButtonText: {
    fontSize: rv(12),
    color: theme.colors.textSecondary,
    fontFamily: "bold",
  },
  countdownText: {
    alignSelf: "center",
    color: theme.colors.textSecondary,
    fontFamily: "medium",
  },
  buttonContainer: {
    width: wp("90%"),
    marginTop: 30,
    flexDirection: "column",
  },
  verifyButton: {
    borderRadius: 14,
    marginTop: 0,
  },
  logoutButton: {
    alignSelf: "center",
    marginLeft: 5,
    marginTop: 15,
  },
  logoutButtonText: {
    fontSize: rv(12),
    color: theme.colors.error,
    fontFamily: "bold",
  },
  pinCodeText: {
    fontSize: rv(12),
    color: theme.colors.textSecondary,
    fontFamily: "medium",
  },
  placeholderText: {
    fontSize: rv(12),
    color: theme.colors.textSecondary,
    fontFamily: "medium",
  },
  otpDescriptionText: {
    marginVertical: 10,
    fontSize: rv(12),
    color: theme.colors.textSecondary,
    fontFamily: "medium",
  },
  otpContainer: {
    width: wp("80%"),
    alignSelf: "center",
    marginVertical: 10,
  },
  emailText: {
    color: theme.colors.buttonPrimary,
    fontFamily: "bold",
    fontSize: rv(12),
    textDecorationLine: "underline",
  },
}));

export default VerifyEmailContainer;
