import React, { useState, useEffect } from 'react';
import {
  View,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Image,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import {
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { CustomText, CustomButton } from 'app/components/elements';
import Loader from 'app/components/elements/Loader';
import { CommonStyles } from 'app/assets/styles';
import { Axios } from 'app/api/axios';
import { showModal, hideModal } from 'app/providers/modals';
import { useTranslation } from 'react-i18next';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import ProfessionalSVG from '@/app/assets/svg/connectifyHorizontallogo.svg';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme, createThemedStyles } from 'app/theme';
import { Theme } from 'app/types/theme';

// Container Component
const CompanyContainer: React.FC<{ navigation: any }> = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [loading, setLoading] = useState(false);
  const [company, setCompany] = useState('');
  const [company_position, setCompanyPosition] = useState('');
  const { t } = useTranslation();

  const updateProfile = () => {
    // Check if fields are filled
    if (!company || !company_position) {
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'Please fill in all fields before proceeding.',
        setModalVisible: hideModal,
        type: 'alert',
        handleConfirm: hideModal,
        handleAlert: hideModal,
      });
      return;
    }

    setLoading(true);

    const formData = {
      company_position,
      company,
    };
  


    Axios({
      method: 'POST',
      url: '/user/update-profile',
      data: formData,

    })
      .then(() => {
        showModal({
          modalVisible: true,
          title: 'Success',
          message: 'Updated successfully',
          setModalVisible: hideModal,
          type: 'success-alert',
          handleConfirm: hideModal,
          handleAlert: hideModal,
        });
        navigation.navigate('status');
      })
      .finally(() => setLoading(false));
  };

  const saveFormData = async () => {
    try {
      await AsyncStorage.setItem('personalformData', JSON.stringify({ company, company_position }));
    } catch (error) {
      console.error('Error saving form data', error);
    }
  };

  const loadFormData = async () => {
    try {
      const storedData = await AsyncStorage.getItem('personalformData');
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        setCompany(parsedData.company || '');
        setCompanyPosition(parsedData.company_position || '');
       
      }
    } catch (error) {
      console.error('Error loading form data', error);
    }
  };
  
  // Call loadFormData when the component mounts
  useEffect(() => {
    loadFormData();
  }, []);
  
  // Update storage when values change
  useEffect(() => {
    saveFormData();
  }, [company, company_position]);
  
  return (
    <CompanyPresentation
      theme={theme}
      styles={styles}
      loading={loading}
      company={company}
      company_position={company_position}
      t={t}
      setCompany={setCompany}
      setCompanyPosition={setCompanyPosition}
      updateProfile={updateProfile}
      navigation={navigation}
    />
  );
};

// Presentational Component
interface CompanyPresentationProps {
  theme: Theme;
  styles: any;
  loading: boolean;
  company: string;
  company_position: string;
  t: any;
  setCompany: (value: string) => void;
  setCompanyPosition: (value: string) => void;
  updateProfile: () => void;
  navigation: any;
}

const CompanyPresentation: React.FC<CompanyPresentationProps> = ({
  theme,
  styles,
  loading,
  company,
  company_position,
  t,
  setCompany,
  setCompanyPosition,
  updateProfile,
  navigation,
}) => {
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView style={styles.scrollView}>
        <Loader loading={loading} />
        <View style={styles.headerContainer}>
          <CustomText style={styles.headerText}>
            {t('Signup_beProfessional')}
          </CustomText>
          <ProfessionalSVG />
        </View>

        <CustomText style={styles.descriptionText}>
          Showcase your career—tell us where you work and your role.
        </CustomText>

        <View style={styles.fieldContainer}>
          <CustomText style={styles.fieldLabel}>
            {t('Signup_placeOfWork')}
          </CustomText>
          <TextInput
            style={[CommonStyles.inputField, { color: theme.colors.text }]}
            onChangeText={setCompany}
            placeholder={t('profile_company')}
            placeholderTextColor={theme.colors.textPlaceholder}
            value={company}
          />
        </View>

        <View style={styles.fieldContainer}>
          <CustomText style={styles.fieldLabel}>
            {t('Signup_jobTitle')}
          </CustomText>
          <TextInput
            style={[CommonStyles.inputField, { color: theme.colors.text }]}
            onChangeText={setCompanyPosition}
            placeholder={t('Signup_jobTitle')}
            placeholderTextColor={theme.colors.textPlaceholder}
            value={company_position}
          />
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity onPress={() => navigation.navigate('personal')}>
            <CustomText style={styles.backButtonText} textType="medium">
              Back
            </CustomText>
          </TouchableOpacity>
          <CustomButton
            label={t('Signup_nextbtn')}
            onPress={updateProfile}
            style={styles.nextButton}
            loading={loading}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};
// Create themed styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: theme.colors.background,
    paddingTop: rv(10),
  },
  scrollView: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingHorizontal: wp('5'),
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: rv(10),
  },
  headerText: {
    color: theme.colors.textSecondary,
    fontSize: rv(21),
    fontFamily: 'medium',
  },
  descriptionText: {
    marginTop: rv(12),
    color: theme.colors.textSecondary,
    fontSize: rv(13),
    fontFamily: 'medium',
  },
  fieldContainer: {
    marginTop: rv(20),
  },
  fieldLabel: {
    color: theme.colors.text,
    fontSize: rv(12),
    fontFamily: 'medium',
  },
  buttonContainer: {
    marginTop: rv(130),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButtonText: {
    color: theme.colors.text,
    fontSize: rv(13),
  },
  nextButton: {
    width: '30%',
    maxWidth: rv(118),
    borderRadius: rv(12),
  },
}));

export default CompanyContainer;
