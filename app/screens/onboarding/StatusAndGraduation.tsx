import React, { FunctionComponent, useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { CustomButton, CustomText } from 'app/components/elements';
import { navigate } from 'app/navigation/root';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import Loader from 'app/components/elements/Loader';
import { useTranslation } from 'react-i18next';
import AlumniSVG from '@/app/assets/svg/alumnus-icon.svg'
import StudentSVG from '@/app/assets/svg/student-icon.svg'
import StaffSVG from '@/app/assets/svg/staff-icon.svg'
import { Icon } from 'react-native-paper';
import CustomCheckbox from 'app/components/elements/CustomCheckBox';
import Upvector from '@/app/assets/svg/up-vector.svg'
import DownVector from '@/app/assets/svg/down-vector.svg'
import StatusSVG from '@/app/assets/svg/connectifyHorizontallogo.svg';
import { widthPercentageToDP as wp } from 'react-native-responsive-screen';
import { Axios } from 'app/api/axios';
import { hideModal, showModal } from 'app/providers/modals';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useTheme, createThemedStyles } from 'app/theme';
import { Theme } from 'app/types/theme';
type Status = '' | 'Student' | 'Alumnus' | 'Staff';

// Container Component
const StatusAndGraduationContainer: FunctionComponent<{
    navigation: any;
}> = ({ navigation }) => {
    const { theme } = useTheme();
    const styles = createStyles(theme);
    const { t } = useTranslation();

    const [status, setStatus] = useState<Status>('');
    console.log(status, 'status')
    const [graduationYear, setGraduationYear] = useState<string>('');
    console.log(graduationYear, 'year')
    const [loading, setLoading] = useState(false);
    const [isStatusDropdownVisible, setStatusDropdownVisible] = useState(false);
    const [isGraduationYearDropdown, setGraduationYearDropdown] = useState(false);
    const [selectedStatus, setSelectedStatus] = useState('')

    const saveFormData = async () => {
        try {
          await AsyncStorage.setItem('formData', JSON.stringify({ status, graduationYear}));
        } catch (error) {
          console.error('Error saving form data', error);
        }
      };
    
      const loadFormData = async () => {
        try {
          const storedData = await AsyncStorage.getItem('formData');
          if (storedData) {
            const parsedData = JSON.parse(storedData);
            setStatus(parsedData.status || '');
            setGraduationYear(parsedData.graduationYear || '');
           
          }
        } catch (error) {
          console.error('Error loading form data', error);
        }
      };
      
      // Call loadFormData when the component mounts
      useEffect(() => {
        loadFormData();
      }, []);
      
      // Update storage when values change
      useEffect(() => {
        saveFormData();
      }, [status, graduationYear]);

    const statusOptions = [
        { id: 'Student', name: 'Student', icon: <StudentSVG /> },
        { id: 'Alumnus', name: 'Alumnus', icon: <AlumniSVG /> },
        { id: 'Staff', name: 'Staff', icon: <StaffSVG /> }
    ];

    const years = Array.from({ length: 81 }, (_, i) => ({
        id: (1950 + i).toString(),
        name: (1950 + i).toString()
    }));

    const handleStatusSelect = (selectedStatus: string) => {
        setStatus(selectedStatus);
        setStatusDropdownVisible(false);
        if (selectedStatus === 'Staff') {
            setGraduationYear('N/A');
        } else {
            setGraduationYear('');
        }
    };

    const updateProfile = async () => {
        if(!status || !graduationYear){
            showModal({
                modalVisible: true,
                title: 'Alert',
                message: 'Pls select a field before proceeding.',
                setModalVisible: hideModal,
                type: 'alert',
                handleConfirm: hideModal,
                handleAlert: hideModal,
              });
              return;
        }
        setLoading(true);
      
        const formData = {
          graduation_status: status,
          graduation_year: graduationYear
        };
        console.log(formData, 'formdata')
      
        try {
          const response = await Axios.post('/user/update-profile', formData);
          
          console.log(response.data, 'data-res');
      
          showModal({
            modalVisible: true,
            title: 'Success',
            message: 'Updated successfully',
            setModalVisible: hideModal,
            type: 'success-alert',
            handleConfirm: hideModal,
            handleAlert: hideModal,
          });
      
          navigation.navigate('area-of-interest');
        } catch (error) {
          console.error('Error updating profile:', error);
      
          showModal({
            modalVisible: true,
            title: 'Error',
            message: 'Failed to update profile. Please try again.',
            setModalVisible: hideModal,
            type: 'error-alert',
            handleConfirm: hideModal,
            handleAlert: hideModal,
          });

        } finally {
          setLoading(false);
        }
      };
      

    return (
        <StatusAndGraduationPresentation
            theme={theme}
            styles={styles}
            loading={loading}
            status={status}
            graduationYear={graduationYear}
            isStatusDropdownVisible={isStatusDropdownVisible}
            isGraduationYearDropdown={isGraduationYearDropdown}
            statusOptions={statusOptions}
            years={years}
            t={t}
            setStatusDropdownVisible={setStatusDropdownVisible}
            setGraduationYearDropdown={setGraduationYearDropdown}
            handleStatusSelect={handleStatusSelect}
            setGraduationYear={setGraduationYear}
            updateProfile={updateProfile}
            navigation={navigation}
        />
    );
};

// Presentational Component
interface StatusAndGraduationPresentationProps {
    theme: Theme;
    styles: any;
    loading: boolean;
    status: Status;
    graduationYear: string;
    isStatusDropdownVisible: boolean;
    isGraduationYearDropdown: boolean;
    statusOptions: any[];
    years: any[];
    t: any;
    setStatusDropdownVisible: (visible: boolean) => void;
    setGraduationYearDropdown: (visible: boolean) => void;
    handleStatusSelect: (status: string) => void;
    setGraduationYear: (year: string) => void;
    updateProfile: () => void;
    navigation: any;
}

const StatusAndGraduationPresentation: React.FC<StatusAndGraduationPresentationProps> = ({
    theme,
    styles,
    loading,
    status,
    graduationYear,
    isStatusDropdownVisible,
    isGraduationYearDropdown,
    statusOptions,
    years,
    t,
    setStatusDropdownVisible,
    setGraduationYearDropdown,
    handleStatusSelect,
    setGraduationYear,
    updateProfile,
    navigation,
}) => {
    return (
        <View style={styles.container}>
            <Loader loading={loading} />
            <View style={styles.headerContainer}>
                <CustomText style={styles.headerText}>
                    Status
                </CustomText>
                <StatusSVG />
            </View>
            {/* Status Dropdown */}
            <CustomText style={styles.descriptionText}>
                What is your current status?
            </CustomText>
            <View style={styles.section}>
                <Text style={styles.label}>Status</Text>

                {/* Status Input/Dropdown Trigger */}
                <TouchableOpacity
                    style={styles.dropdownTrigger}
                    onPress={() => setStatusDropdownVisible(!isStatusDropdownVisible)}
                >
                    <Text style={styles.placeholderText}>
                        Are you a student, alumnus or staff?
                    </Text>

                    <Ionicons
                        name={'chevron-down'}
                        size={rv(14)}
                        style={styles.arrow}
                        />

                </TouchableOpacity>

                {/* Selected Status Display */}
                {status && (
                    <View style={styles.selectedItemContainer}>
                        <View style={styles.selectedItem}>
                            {statusOptions.find(item => item.id === status)?.icon}
                            <Text style={styles.selectedItemText}>{status}</Text>
                        </View>
                    </View>
                )}

                {/* Status Dropdown */}
                {isStatusDropdownVisible && (
                    <View style={styles.dropdownContainer}>
                        {statusOptions.map((item) => (
                            <TouchableOpacity
                                key={item.id}
                                style={styles.dropdownItem}
                                onPress={() => handleStatusSelect(item.id)}
                            >
                                {item.icon}
                                <Text style={styles.dropdownItemText}>{item.name}</Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                )}
            </View>

            {/* Graduation Year List */}
            <View style={styles.section}>
                <Text style={styles.label}>Year of Graduation</Text>
                {status === 'Staff' ? (
                    <Text style={styles.notApplicable}>Not Application</Text>
                ) : (
                    <>
                        <TouchableOpacity
                            style={styles.dropdownTrigger}
                            onPress={() => setGraduationYearDropdown(!isGraduationYearDropdown)}
                        >
                            <Text style={styles.placeholderText}>
                                {'What year did you graduate?'}
                            </Text>
                            <Ionicons
                                name={'chevron-down'}
                                size={rv(14)}
                                style={styles.arrow}
                            />

                        </TouchableOpacity>

                        {isGraduationYearDropdown && (
                            <FlatList
                                data={years}
                                keyExtractor={(item: { id: any; }) => item.id}
                                renderItem={({ item }) => (
                                    <TouchableOpacity
                                        onPress={() => setGraduationYear(item.id)}
                                        style={[
                                            styles.yearItem,
                                            graduationYear === item.id && styles.selectedYearItem
                                        ]}
                                    >
                                         <CustomCheckbox
                                            value={graduationYear === item.id}
                                            onValueChange={() => setGraduationYear(item.id)}
                                            color="green"
                                            size={15}
                                        />
                                        <Text style={[
                                            styles.yearText,
                                            graduationYear === item.id && styles.selectedYearText
                                        ]}>
                                            {item.name}
                                        </Text>
                                       

                                    </TouchableOpacity>
                                )}
                                style={styles.yearList}
                            />
                        )}
                    </>
                )}
            </View>

            <View style={styles.buttonContainer}>
                <TouchableOpacity onPress={()=> navigation.navigate('company')}>
                    <CustomText style={styles.backButton} textType="medium">
                        Back
                    </CustomText>
                </TouchableOpacity>
                <CustomButton
                    label={t('Signup_nextbtn')}
                    onPress={updateProfile}
                    style={styles.nextButton}
                    loading={loading}
                    disabled={loading}
                />
            </View>
        </View>
    );
};

// Create themed styles
const createStyles = createThemedStyles((theme) => ({
    container: {
        flex: 1,
        paddingHorizontal: wp('5'),
        paddingTop: rv(20),
        backgroundColor: theme.colors.background,
    },
    headerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingTop: rv(10),
    },
    headerText: {
        color: theme.colors.textSecondary,
        fontSize: rv(21),
        fontFamily: 'medium',
    },
    descriptionText: {
        color: theme.colors.textSecondary,
        fontSize: rv(13),
        fontFamily: 'medium',
    },
    section: {
        marginBottom: rv(20)
    },
    label: {
        fontSize: rv(12),
        fontFamily: 'medium',
        marginBottom: rv(10),
        color: theme.colors.text,
        marginTop: rv(20)
    },
    dropdownTrigger: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: rv(12),
        borderWidth: 1,
        borderColor: theme.colors.border,
        borderRadius: rv(8),
        marginBottom: rv(10),
        backgroundColor: theme.colors.inputBackground,
    },
    placeholderText: {
        color: theme.colors.text,
        fontSize: rv(12),
        fontFamily: 'medium'
    },
    dropdownContainer: {
        position: 'absolute',
        top: rv(80),
        left: 0,
        right: 0,
        backgroundColor: theme.colors.surface,
        borderRadius: rv(8),
        borderWidth: 1,
        borderColor: theme.colors.border,
        zIndex: 1000,
        elevation: 5
    },
    dropdownItem: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: rv(12),
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.border
    },
    dropdownItemText: {
        marginLeft: rv(10),
        fontSize: rv(13),
        color: theme.colors.textSecondary,
        fontFamily: 'medium',
    },
    selectedItemContainer: {
        borderColor: theme.colors.border,
        borderRadius: 6,
        borderWidth: 1,
        padding: rv(10),
        marginTop: rv(10),
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: theme.colors.inputBackground,
    },
    selectedItem: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: theme.colors.surfaceVariant,
        padding: rv(8),
        borderRadius: rv(20),
        marginRight: rv(8),
        marginBottom: rv(8)
    },
    selectedItemText: {
        marginLeft: rv(8),
        fontSize: rv(12),
        fontFamily: 'medium',
        color: theme.colors.text,
    },
    yearList: {
        maxHeight: rv(200)
    },
    yearItem: {
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.border,
        display: 'flex',
        alignItems: 'center',
        flexDirection: 'row',
        padding: 10,
        gap: 10
    },
    selectedYearItem: {
        backgroundColor: theme.colors.surfaceVariant
    },
    yearText: {
        fontSize: rv(13),
        color: theme.colors.textSecondary,
        fontFamily: 'medium',
    },
    selectedYearText: {
        color: theme.colors.primary
    },
    buttonContainer: {
        marginTop: rv(30),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    backButton: {
        color: theme.colors.text,
        fontSize: rv(13)
    },
    nextButton: {
        width: '30%',
        maxWidth: rv(118),
        borderRadius: rv(12)
    },
    arrow: {
        fontSize: rv(18),
        color: theme.colors.textSecondary,
    },
    notApplicable: {
        fontSize: rv(14),
        fontFamily: 'medium',
        color: theme.colors.textSecondary
    }
}));

export default StatusAndGraduationContainer;