import React, { useState, useEffect, useContext } from 'react';
import {
  Text,
  View,
  SafeAreaView,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  StyleSheet,
} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Ionicons from '@expo/vector-icons/Ionicons';
import { default as themeFont } from 'app/assets/themes/fonts.json';
import { CustomText, CustomButton } from 'app/components/elements';
import { useCommonStyles } from 'app/assets/styles/CommonStyles';
import HeaderTitle from 'app/components/HeaderTitle';
import {
  useGetHelpDeskCategories,
  useCreateTicket,
} from '../../../redux/helpdesk/hooks';
import SelectDropdown from 'react-native-select-dropdown';
import { useTranslation } from 'react-i18next';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import CustomModal from 'app/components/elements/Modals';
import { useTheme, createThemedStyles } from 'app/theme';

type HelpDeskScreenProps = {
  navigation: any;
};


const CreateHelpDeskTicket: React.FC<HelpDeskScreenProps> = ({
  navigation,
}) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const commonStyles = useCommonStyles();
  const [category, setCategory] = useState('');
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const { data: categories } = useGetHelpDeskCategories();
  const { mutateAsync: createTicket, isLoading } = useCreateTicket();
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const [alertMessage,setAlertMessage] = useState("")
  const [type,setType] = useState<any>(null)
  const handleNavigateChallengeMode = () => {
    navigation.goBack();
    // You can do some validation here before navigating
    return
  }

  useEffect(() => {
    if (type !== null) { // Ensure type has been set before showing the modal
      setModalVisible(true);
    }
  }, [type]);
  

  async function submit() {
    let payload = {
      title,
      category,
      message: content,
    };
    if (!payload.title || !payload.category || !payload.message) {
      setType(false)
      setModalVisible(true);
      return; // Stop execution if validation fails
  }
      setType(true)
      createTicket(payload).then((response: any) => {
      setModalVisible(true);
      setContent('');
      setTitle('');
      
    });
  }

  const handleConfirm = () => {
    // You can do some validation here before navigating
    navigation.navigate('helpdesk' as never);
    setModalVisible(false); 
  };

  return (
    <SafeAreaView style={styles.safeAreaContainer}>
      <ScrollView style={styles.scrollViewContainer}>
        {modalVisible && (
            <CustomModal
            modalVisible={modalVisible}
            setModalVisible={setModalVisible}
            NavigateToCreditPage={handleNavigateChallengeMode}
            type={type ? "helpdesk" : "alert"}
            message={"All fields are required"} // or "stake" based on the scenario
            handleConfirm={handleConfirm}
            navigation
          /> )}
        <StatusBar barStyle={theme.colors.statusBarStyle} backgroundColor={theme.colors.background} />
        <HeaderTitle title={t('HelpDesk_')} navigation={navigation} />
        <View
          style={{
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <View style={{ width: '87%' }}>
            <CustomText style={styles.helpText}>
              {t('HelpDesk_helpText')}
            </CustomText>

            <CustomText style={styles.categoryLabel}>
              {t('HelpDesk_category')}
            </CustomText>

            {categories ? (
              <View>
                <SelectDropdown
                  data={categories}
                  onSelect={(selectedItem: any, index: any) => {
                    console.log(selectedItem, index);
                    setCategory(selectedItem);
                  }}
                  renderDropdownIcon={(isOpened: any) => {
                    return (
                      <Ionicons
                        name={isOpened ? 'chevron-up' : 'chevron-down'}
                        size={20}
                        color='gray'
                      />
                    );
                  }}
                  dropdownIconPosition={'right'}
                  buttonStyle={[
                    commonStyles.inputField,
                    styles.dropdownButton,
                    { width: '100%' }
                  ]}
                  buttonTextStyle={styles.dropdownButtonText}
                  rowTextStyle={styles.dropdownRowText}
                  rowStyle={styles.dropdownRow}
                  dropdownStyle={styles.dropdownContainer}
                  defaultButtonText={t('HelpDesk_categroryDrop')}
                  buttonTextAfterSelection={(selectedItem: any, index: any) => {
                    // text represented after item is selected
                    // if data array is an array of objects then return selectedItem.property to render after item is selected
                    return selectedItem;
                  }}
                  search={true}
                  rowTextForSelection={(item: any, index: any) => {
                    // text represented for each item in dropdown
                    // if data array is an array of objects then return item.property to represent item in dropdown
                    return item;
                  }}
                />
              </View>
            ) : null}

            <CustomText style={styles.messageTitleLabel}>
              {t('HelpDesk_messageTitle')}
            </CustomText>

            <TextInput
              style={styles.textInput}
              maxLength={50}
              placeholder={t('HelpDesk_messageTitle')}
              placeholderTextColor={theme.colors.textPlaceholder}
              onChangeText={(text: any) => {
                setTitle(text);
              }}
              value={title}
            />

            <CustomText style={styles.detailsLabel}>
              {t('HelpDesk_details')}
            </CustomText>

            <TextInput
              multiline={true}
              numberOfLines={10}
              placeholder={t('HelpDesk_messageBox')}
              placeholderTextColor={theme.colors.textPlaceholder}
              value={content!}
              style={styles.messageTextInput}
              underlineColorAndroid='transparent'
              onChangeText={(contents) => setContent(contents)}
              maxLength={200}
            />

            <View
              style={{
                width: wp('90'),
                marginTop: rv(120),
              }}
            >
              <CustomButton
                label={t('HelpDesk_sendBtn')}
                onPress={() => submit()}
                loading={isLoading}
                buttonTheme='primary'
              />
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = createThemedStyles((theme) => ({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollViewContainer: {
    backgroundColor: theme.colors.background,
  },
  helpText: {
    fontSize: rv(13),
    fontFamily: 'semiBold',
    color: theme.colors.text,
  },
  categoryLabel: {
    marginTop: 20,
    fontSize: rv(13),
    fontFamily: 'semiBold',
    color: theme.colors.text,
  },
  dropdownButton: {
    backgroundColor: theme.colors.inputBackground,
    borderColor: theme.colors.inputBorder,
  },
  dropdownButtonText: {
    fontFamily: 'semiBold',
    fontSize: rv(12),
    color: theme.colors.text,
  },
  dropdownRowText: {
    fontFamily: 'semiBold',
    fontSize: rv(13),
    color: theme.colors.text,
  },
  dropdownRow: {
    borderBottomWidth: 0,
    backgroundColor: theme.colors.background,
  },
  dropdownContainer: {
    backgroundColor: theme.colors.background,
    borderColor: theme.colors.border,
  },
  messageTitleLabel: {
    marginTop: 10,
    fontSize: rv(12),
    fontFamily: 'semiBold',
    color: theme.colors.text,
  },
  detailsLabel: {
    marginTop: 10,
    fontSize: rv(13),
    fontFamily: 'semiBold',
    color: theme.colors.text,
  },
  textInput: {
    height: 60,
    borderRadius: 14,
    paddingLeft: 20,
    backgroundColor: theme.colors.surface,
    marginBottom: 15,
    borderStyle: 'solid',
    marginTop: 10,
    fontFamily: 'semi-bold',
    fontSize: rv(13),
    color: theme.colors.text,
  },
  messageTextInput: {
    backgroundColor: theme.colors.surface,
    height: hp('15%'),
    textAlignVertical: 'top',
    fontFamily: 'semi-bold',
    fontSize: rv(13),
    color: theme.colors.text,
    lineHeight: 20,
    borderRadius: 14,
    paddingLeft: 20,
    paddingTop: 15,
    marginTop: 10,
  },
}));

export default CreateHelpDeskTicket;
