import React, { useEffect } from 'react';
import {
  View,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  Image,
} from 'react-native';
import {
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { CustomText, CustomButton } from 'app/components/elements';
import HeaderTitle from 'app/components/HeaderTitle';
import { useGetHelpDeskTickets } from '../../../redux/helpdesk/hooks';
import { timeAgo } from 'app/helpers/time-ago';
import { useTranslation } from 'react-i18next';
import { StackNavigationProp } from '@react-navigation/stack';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import BlankHelpdeskSVG  from 'app/assets/svg/empty-pages.svg';
import HelpdeskTextSVG from '@/app/assets/svg/helpdeskText.svg'
import { useTheme, createThemedStyles } from 'app/theme';

const Loading = require('../../../assets/loading.gif');
const defaultImage = require('../../../assets/connectify-thumbnail.png');

type RootStackParamList = {
  // Other screens and their parameters
};

type HelpDeskScreenProps = {
  navigation: StackNavigationProp<RootStackParamList, 'Helpdesk'>; // Specify the navigation prop type
};

// Create themed styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    backgroundColor: theme.colors.background,
    flex: 1,
  },
  scrollContainer: {
    flexDirection: 'column' as const,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  contentWrapper: {
    width: '90%' as const,
    flex: 1,
    flexDirection: 'column' as const,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  ticketItem: {
    flex: 1,
    flexDirection: 'row' as const,
    marginVertical: 10,
  },
  avatarContainer: {
    marginRight: rv(15),
  },
  ticketContent: {
    flex: 1,
    flexDirection: 'column' as const,
  },
  ticketHeader: {
    width: '100%' as const,
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
  },
  titleContainer: {
    width: '90%' as const,
  },
  titleText: {
    flexWrap: 'nowrap' as const,
    fontSize: rv(12),
    color: theme.colors.text,
    maxWidth: '100%' as const,
    fontFamily: 'bold',
  },
  timeText: {
    fontSize: rv(10),
    color: theme.colors.textSecondary,
  },
  categoryText: {
    fontSize: rv(13),
    color: theme.colors.textSecondary,
    flexWrap: 'nowrap' as const,
    alignSelf: 'flex-start' as const,
  },
  emptyContainer: {
    alignContent: 'center' as const,
    marginTop: '30%' as const,
  },
  emptyTextContainer: {
    paddingHorizontal: wp('10'),
  },
  floatingButton: {
    position: 'absolute' as const,
    width: 130,
    height: 50,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    right: 30,
    bottom: 50,
  },
}));

const HelpDesk: React.FC<HelpDeskScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const {
    data: helpDeskTickets,
    isLoading,
  } = useGetHelpDeskTickets();
  const { t } = useTranslation();

  useEffect(() => {
    console.log(helpDeskTickets, 'tickets');
  }, [helpDeskTickets]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle={theme.colors.statusBarStyle}
        backgroundColor={theme.colors.background}
      />
      <HeaderTitle title={t('HelpDesk_')} navigation={navigation} />
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
      >
        <View style={styles.contentWrapper}>
          <View>

          </View>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <Image
                source={Loading}
                style={{
                  width: rv(50),
                  height: rv(50),
                }}
              />
            </View>
          ) : (helpDeskTickets && helpDeskTickets.length > 0) ? (
            <>
              {helpDeskTickets.map((item: any, index: number) => (
                <TouchableOpacity
                  key={index}
                  style={styles.ticketItem}
                  onPress={() =>
                    navigation.navigate('helpdesk-messages', {
                      helpdeskId: item._id,
                      helpdesk: item,
                    })
                  }
                >
                  <View style={styles.avatarContainer}>
                    <Image
                      source={defaultImage}
                      style={{
                        width: rv(35),
                        height: rv(35),
                      }}
                    />
                  </View>
                  <View style={styles.ticketContent}>
                    <View style={styles.ticketHeader}>
                      <View style={styles.titleContainer}>
                        <CustomText style={styles.titleText}>
                          {item.title}
                        </CustomText>
                      </View>

                      <View>
                        <CustomText style={styles.timeText}>
                          {timeAgo(item.createdAt)}
                        </CustomText>
                      </View>
                    </View>

                    <CustomText style={styles.categoryText}>
                      {t('HelpDesk_category')}: {item.category}
                    </CustomText>
                  </View>
                </TouchableOpacity>
              ))}
            </>
          ) : (
            <View style={styles.emptyContainer}>
              <BlankHelpdeskSVG style={{ alignSelf: 'center' }} />
              <View style={styles.emptyTextContainer}>
                <HelpdeskTextSVG style={{ alignSelf: 'center', marginTop: rv(20) }} />
              </View>
            </View>
          )}


        </View>
      </ScrollView>

      <View style={styles.floatingButton}>
        <CustomButton
          label={t('HelpDesk_compose')}
          textSize={rv(13)}
          style={{
            height: rv(55),
            alignItems: 'center',
          }}
          onPress={() => navigation.navigate('create-ticket')}
        />
      </View>
    </SafeAreaView>
  );
};

export default HelpDesk;
