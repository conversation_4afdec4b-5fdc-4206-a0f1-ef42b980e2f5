import React, { useState, useContext, useEffect } from 'react';
import { View, TextInput, ScrollView, SafeAreaView } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Loader from 'app/components/elements/Loader';
import { CustomButton, CustomText } from 'app/components/elements';
import HeaderTitle from 'app/components/HeaderTitle';
import SelectInterests from 'app/components/elements/SelectInterests';
import { Axios } from 'app/api/axios';
import { useDispatch, useSelector } from 'react-redux';
import { userAuthInfo,updateUser } from 'app/redux/user/reducer';
import { ShowAlert } from 'app/providers/toast';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useTranslation } from 'react-i18next';
import { hideModal, showModal } from 'app/providers/modals';
import { updateUserInfo } from 'app/redux/user/hooks';
import { useTheme } from 'app/hooks/useTheme';
import { createThemedStyles } from 'app/utils/createThemedStyles';

type SelectAreaOfInterestScreenProps = {
  navigation: any;
};

// Create themed styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    backgroundColor: theme.colors.background,
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  innerContainer: {
    width: '85%',
    height: hp('80'),
    marginTop: 20,
  },
  textContainer: {
    flexDirection: 'column',
  },
  descriptionText: {
    color: theme.colors.textSecondary,
    fontSize: rv(12),
    marginBottom: 20,
    fontFamily: 'semiBold'
  },
  buttonContainer: {
    marginTop: 'auto',
  },
}));

const AreaOfInterest: React.FC<SelectAreaOfInterestScreenProps> = ({
  navigation,
}) => {
  const profile = useSelector(userAuthInfo);
  const { t } = useTranslation();
  const dispatch = useDispatch()
  const [interests, setInterests] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const { theme } = useTheme();
  const styles = createStyles(theme);

  useEffect(() => {
    const loadInterests = async () => {
      setLoading(true); // Ensure loading state is set to true initially
      try {
        // Simulate fetching profile interests (or replace this with an actual async call)
        if (profile && profile.interests) {
          setInterests(profile.interests);
        }
      } catch (error) {
        console.error("Error loading interests:", error);
      } finally {
        setLoading(false); // Hide the loader once loading is complete
      }
    };

    loadInterests(); // Call the loadInterests function to fetch data
  }, []);

  function updateAreaOfInterests() {

    setLoading(true);
    if (interests.length === 0) {
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: "Select at least one tag",
        setModalVisible: hideModal, // Function to close the modal
        type: 'alert',
        handleConfirm: () => {
          hideModal();
        },
        handleAlert: () => {
          hideModal();
        },
        navigation
      });
      setLoading(false); // Stop loading state
      return; // Exit the function early
    }

    Axios({
      method: 'post',
      url: '/tags/set-user-tags',
      data: interests,
    })
      .then(() => {
        updateUserInfo();
        showModal({
          modalVisible: true,
          title: 'Success',
          message: "Your interests have been updated",
          setModalVisible: hideModal, // Function to close the modal
          type: 'success-alert',
          handleConfirm: () => {
            hideModal();
            
          },
          handleAlert: () => {
            hideModal();
            navigation.goBack()
          },
          navigation
        });
        
      })
      .finally(() => {
        setLoading(false);
      });
  }
  console.log(loading,"loading")
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <HeaderTitle title={t('Signup_areas_of_interest')} navigation={navigation} />
        <View style={styles.contentContainer}>
          <View style={styles.innerContainer}>
            <View style={styles.textContainer}>
              {loading ? (
                <Loader loading={loading} />
              ):
              (
                <CustomText style={styles.descriptionText}>
                  {t('AreasOfInterest_text')}
                </CustomText>
              )}

              {loading ? (
                <Loader loading={loading} />
              ) : (
                <SelectInterests
                  interests={interests}
                  setInterests={setInterests}
                  multiple={true}
                />
              )}

            </View>
            <View style={styles.buttonContainer}>
              <CustomButton
                label={t("AreasOfInterest_button")}
                onPress={() => updateAreaOfInterests()}
              />
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default AreaOfInterest;
