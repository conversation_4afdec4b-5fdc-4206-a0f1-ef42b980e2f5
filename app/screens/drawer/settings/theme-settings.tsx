import React from 'react';
import { View, ScrollView, SafeAreaView } from 'react-native';
import { useTheme } from 'app/hooks/useTheme';
import { createThemedStyles } from 'app/utils/createThemedStyles';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useTranslation } from 'react-i18next';
import HeaderTitle from 'app/components/HeaderTitle';
import { ThemeSelector } from 'app/components/ThemeSelector';
import { CustomText } from 'app/components/elements';

type ThemeSettingsScreenProps = {
  navigation: any;
};

// Container Component
const ThemeSettingsContainer: React.FC<ThemeSettingsScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  return (
    <ThemeSettingsPresentation
      theme={theme}
      styles={styles}
      navigation={navigation}
      t={t}
    />
  );
};

// Presentation Component
interface ThemeSettingsPresentationProps {
  theme: any;
  styles: any;
  navigation: any;
  t: any;
}

const ThemeSettingsPresentation: React.FC<ThemeSettingsPresentationProps> = ({
  theme,
  styles,
  navigation,
  t,
}) => {
  return (
    <SafeAreaView style={styles.container}>
      <HeaderTitle title="Theme Settings" navigation={navigation} />
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.section}>
            <CustomText style={styles.sectionTitle}>
              Choose Theme
            </CustomText>
            <CustomText style={styles.sectionDescription}>
              Select your preferred theme. System will automatically switch between light and dark based on your device settings.
            </CustomText>
            
            <View style={styles.themeSelectorContainer}>
              <ThemeSelector />
            </View>
          </View>

          <View style={styles.section}>
            <CustomText style={styles.sectionTitle}>
              Current Theme
            </CustomText>
            <CustomText style={styles.currentThemeText}>
              {theme.mode === 'light' ? 'Light Mode' : 'Dark Mode'}
            </CustomText>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// Themed styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: rv(20),
  },
  section: {
    marginBottom: rv(30),
  },
  sectionTitle: {
    fontSize: rv(18),
    fontFamily: 'bold',
    color: theme.colors.text,
    marginBottom: rv(8),
  },
  sectionDescription: {
    fontSize: rv(14),
    fontFamily: 'regular',
    color: theme.colors.textSecondary,
    lineHeight: rv(20),
    marginBottom: rv(20),
  },
  themeSelectorContainer: {
    marginTop: rv(10),
  },
  currentThemeText: {
    fontSize: rv(16),
    fontFamily: 'medium',
    color: theme.colors.primary,
    marginTop: rv(8),
  },
}));

export default ThemeSettingsContainer;
