import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { WebView } from 'react-native-webview';
import HeaderTitle from '../../../components/HeaderTitle';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useTheme, createThemedStyles } from 'app/theme';

type EULAScreenProps = {
  navigation: any;
};

const EULA: React.FC<EULAScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [isLoading, setIsLoading] = useState(true);

  const eulaHTML = `
  <html>
  <head>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-size: 12px;
      line-height: 1.5em;
      background-color: ${theme.colors.background};
      color: ${theme.colors.text};
      margin: 16px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    h2, h3 {
      color: ${theme.colors.text};
    }
    h2 {
      font-size: 14px;
      line-height: 14px;
    }
    h3 {
      font-size: 13px;
      line-height: 13px;
    }
    p, li {
      font-size: 12px;
      line-height: 12px;
      color: ${theme.colors.text};
    }
    strong {
      color: ${theme.colors.text};
    }
  </style>
  </head>
  <body>
  <h2>END-USER LICENSE AGREEMENT</h2>

  <h3>Part 1. Introduction, Acknowledgement, and Definitions</h3>

  <p><strong>1.1 Parties and Acknowledgement</strong></p>
  <p>This End-User License Agreement ("EULA") is a legal agreement between you ("End-User") and Laten Geuberen Limited/Laten Geuberen Enterprises ("Developer") for the Licensed Application provided by Developer. This EULA is concluded solely between the End-User and the Developer, and not with Apple. Apple is not a party to this agreement and has no obligations or liability under this EULA.</p>

  <p><strong>1.2 Definitions</strong></p>
  <ul>
    <li><p style="font-size: 12px; line-height: 12px"><strong>Licensed Application:</strong> The Lucosa software application provided by the Developer for use on Apple-branded products.</p></li>
    <li><p style="font-size: 12px; line-height: 12px"><strong>Apple-Branded Products:</strong> Devices produced or licensed by Apple Inc., on which the Licensed Application may be used as permitted by this EULA and applicable Apple Media Services Terms.</p></li>
    <li><p style="font-size: 12px; line-height: 12px"><strong>End-User:</strong> The individual or entity that installs, accesses, or otherwise uses the Licensed Application.</p></li>
    <li><p style="font-size: 12px; line-height: 12px"><strong>Developer:</strong> Laten Geuberen Limited (Canada) or Laten Geuberen Enterprises (Nigeria).</p></li>
    <li><p style="font-size: 12px; line-height: 12px"><strong>Third Party Terms:</strong> Any applicable terms imposed by third parties (e.g., wireless carriers, platform providers) that govern the use of certain functionalities of the Licensed Application.</p></li>
  </ul>
  
  <h3 style="font-size: 13px; line-height: 13px">Part 2. Grant and Scope of License</h3>
  
  <p style="font-size: 12px; line-height: 12px"><strong>2.1 License Grant</strong></p>
  <p style="font-size: 12px; line-height: 12px">Subject to the terms of this EULA, Developer hereby grants you a limited, non-exclusive, non-transferable, non-sublicensable license to use the Licensed Application solely on Apple-branded products that you own or control. This license permits usage in accordance with the Apple Media Services Terms and any restrictions set forth therein. The Licensed Application may also be accessed by additional accounts associated with the primary purchaser via Family Sharing or volume purchasing arrangements.</p>
  
  <p style="font-size: 12px; line-height: 12px"><strong>2.2 License Restrictions</strong></p>
  <p style="font-size: 12px; line-height: 12px">You may not:</p>
  <ul>
    <li><p style="font-size: 12px; line-height: 12px">Transfer, sell, lease, sublicense, or distribute the Licensed Application or any rights granted herein.</p></li>
    <li><p style="font-size: 12px; line-height: 12px">Modify, adapt, or reverse engineer the Licensed Application except as expressly permitted by applicable law.</p></li>
    <li><p style="font-size: 12px; line-height: 12px">Use the Licensed Application in a manner that violates any applicable third-party agreements or the Apple Media Services Terms.</p></li>
  </ul>
  
  <h3 style="font-size: 13px; line-height: 13px">Part 3. Maintenance, Support, and Product Claims</h3>
  
  <p style="font-size: 12px; line-height: 12px"><strong>3.1 Maintenance and Support</strong></p>
  <p style="font-size: 12px; line-height: 12px">The Developer is solely responsible for providing maintenance and support for the Licensed Application. Apple is not obligated to furnish any maintenance or support services. Any support obligations will be provided solely by the Developer, as described in additional documentation or support policies available on our website.</p>
  
  <p style="font-size: 12px; line-height: 12px"><strong>3.2 Product Warranty and Refunds</strong></p>
  <p style="font-size: 12px; line-height: 12px">Except as expressly provided herein, the Licensed Application is provided "as is" without any warranty, whether express, implied, or statutory. The Developer is solely responsible for any product warranties and any failure of the Licensed Application to conform to such warranties. In the event of a failure to conform to any applicable warranty, you may notify Apple, and Apple will, to the extent permitted by applicable law, refund the purchase price for the Licensed Application. Beyond such refund, Apple shall have no further warranty obligations. Any further claims, losses, or liabilities are solely the responsibility of the Developer.</p>
  
  <p style="font-size: 12px; line-height: 12px"><strong>3.3 Product Claims and Liability</strong></p>
  <p style="font-size: 12px; line-height: 12px">You acknowledge and agree that the Developer, and not Apple, is solely responsible for addressing any product liability claims, claims that the Licensed Application fails to conform to applicable legal or regulatory requirements, and claims arising under consumer protection, privacy, or similar legislation. The Developer will be solely responsible for the investigation, defense, settlement, and discharge of any such claims.</p>
  
  <h3 style="font-size: 13px; line-height: 13px">Part 4. Intellectual Property Rights and Third-Party Infringement</h3>
  
  <p style="font-size: 12px; line-height: 12px"><strong>4.1 Intellectual Property Rights</strong></p>
  <p style="font-size: 12px; line-height: 12px">All title and intellectual property rights in and to the Licensed Application and any associated documentation remain with the Developer or its licensors. In the event of any third-party claim that the Licensed Application or your use of it infringes a third party's intellectual property rights, the Developer shall be solely responsible for the investigation, defense, settlement, and discharge of such claims.</p>
  
  <p style="font-size: 12px; line-height: 12px"><strong>4.2 Third Party Beneficiary</strong></p>
  <p style="font-size: 12px; line-height: 12px">By accepting this EULA, you acknowledge and agree that Apple Inc. and its subsidiaries are intended third-party beneficiaries of this EULA. Accordingly, Apple shall have the right to enforce the provisions of this EULA directly against you as a third-party beneficiary.</p>
  
  <h3 style="font-size: 13px; line-height: 13px">Part 5. Legal Compliance, Representations, and Developer Contact</h3>
  
  <p style="font-size: 12px; line-height: 12px"><strong>5.1 Legal Compliance and End-User Representations</strong></p>
  <p style="font-size: 12px; line-height: 12px">You represent and warrant that:</p>
  <ul>
    <li><p style="font-size: 12px; line-height: 12px">You are not located in any country subject to a U.S. Government embargo or designated by the U.S. Government as a "terrorist supporting" country;</p></li>
    <li><p style="font-size: 12px; line-height: 12px">You are not listed on any U.S. Government list of prohibited or restricted parties; and</p></li>
    <li><p style="font-size: 12px; line-height: 12px">Your use of the Licensed Application will comply with all applicable laws and regulations, as well as any applicable third party terms.</p></li>
  </ul>
  
  <p style="font-size: 12px; line-height: 12px"><strong>5.2 Developer Contact Information</strong></p>
  <p style="font-size: 12px; line-height: 12px">For any questions, complaints, or claims with respect to the Licensed Application, please contact:</p>
  <p style="font-size: 12px; line-height: 12px">Laten Geuberen Limited/Laten Geuberen Enterprises<br>
  Email: <EMAIL></p>
  
  <p style="font-size: 12px; line-height: 12px"><strong>5.3 Third Party Terms of Agreement</strong></p>
  <p style="font-size: 12px; line-height: 12px">Your use of the Licensed Application may be subject to additional third party terms (for example, those imposed by your wireless carrier or Apple's Media Services Terms). You agree to comply with all such applicable third party terms when using the Licensed Application.</p>
  
  <h3 style="font-size: 13px; line-height: 13px">Part 6. Miscellaneous Provisions</h3>
  
  <p style="font-size: 12px; line-height: 12px"><strong>6.1 Governing Law and Jurisdiction</strong></p>
  <p style="font-size: 12px; line-height: 12px">This EULA shall be governed by and construed in accordance with the laws of the Federal Republic of Nigeria. Any disputes arising out of or in connection with this EULA shall be resolved exclusively in the appropriate courts of the Federal Republic of Nigeria, except as otherwise provided by applicable law or the Apple Media Services Terms.</p>
  
  <p style="font-size: 12px; line-height: 12px"><strong>6.2 Severability and Entire Agreement</strong></p>
  <p style="font-size: 12px; line-height: 12px">If any provision of this EULA is held to be invalid or unenforceable, such provision shall be adjusted to the minimum extent necessary to make it enforceable, and the remainder of this EULA shall remain in full force and effect. This EULA, together with any additional terms provided by the Developer, constitutes the entire agreement between you and the Developer regarding the Licensed Application.</p>
  
  <p style="font-size: 12px; line-height: 12px"><strong>6.3 Modification and Termination</strong></p>
  <p style="font-size: 12px; line-height: 12px">The Developer reserves the right to modify this EULA at any time. Any modifications will be posted on our website or within the Licensed Application, and your continued use of the Licensed Application following such modifications constitutes your acceptance of the updated EULA. The Developer may terminate your license if you fail to comply with the terms of this EULA.</p>
  
  <p style="font-size: 12px; line-height: 12px">This Agreement was last modified on March 15, 2025.</p>
  </body>
  </html>
  `;

  const handleNavigation = (event:any) => {
    const data = event.nativeEvent.data;
    if (data.startsWith('navigate:')) {
      const screen = data.split(':')[1];
      navigation.navigate(screen);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle={theme.colors.statusBarStyle} backgroundColor={theme.colors.background} />
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        <HeaderTitle title='End-User License Agreement' navigation={navigation} />
        <View style={styles.container}>
          <WebView
            source={{ html: eulaHTML }}
            onMessage={handleNavigation}
            style={[styles.webView, { opacity: isLoading ? 0 : 1 }]}
            onLoadStart={() => setIsLoading(true)}
            onLoadEnd={() => setIsLoading(false)}
          />
          {isLoading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.accent} />
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// Themed Styles
const createStyles = createThemedStyles((theme) => ({
  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    backgroundColor: theme.colors.background,
  },
  scrollContent: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: 20,
  },
  webView: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  title: {
    fontSize: rv(13),
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: theme.colors.text,
  },
  text: {
    fontSize: rv(13),
    lineHeight: 30,
    marginBottom: 7,
    color: theme.colors.text,
  },
}));

export default EULA; 