import React from 'react';
import { View, ScrollView, SafeAreaView, TouchableOpacity } from 'react-native';
import { useTheme } from 'app/hooks/useTheme';
import { useFontSize } from 'app/hooks/useFontSize';
import { createThemedStyles } from 'app/utils/createThemedStyles';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useTranslation } from 'react-i18next';
import HeaderTitle from 'app/components/HeaderTitle';
import { CustomText } from 'app/components/elements';
import { FontSizeScale, FONT_SIZE_LABELS } from 'app/providers/FontSizeProvider';

type FontSizeSettingsScreenProps = {
  navigation: any;
};

// Container Component
const FontSizeSettingsContainer: React.FC<FontSizeSettingsScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { fontSizeScale, setFontSizeScale } = useFontSize();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  const handleFontSizeChange = (scale: FontSizeScale) => {
    setFontSizeScale(scale);
  };

  return (
    <FontSizeSettingsPresentation
      theme={theme}
      styles={styles}
      navigation={navigation}
      t={t}
      fontSizeScale={fontSizeScale}
      onFontSizeChange={handleFontSizeChange}
    />
  );
};

// Presentation Component
interface FontSizeSettingsPresentationProps {
  theme: any;
  styles: any;
  navigation: any;
  t: any;
  fontSizeScale: FontSizeScale;
  onFontSizeChange: (scale: FontSizeScale) => void;
}

const FontSizeSettingsPresentation: React.FC<FontSizeSettingsPresentationProps> = ({
  theme,
  styles,
  navigation,
  t,
  fontSizeScale,
  onFontSizeChange,
}) => {
  const fontSizeOptions: FontSizeScale[] = ['small', 'normal', 'large', 'extra-large'];

  return (
    <SafeAreaView style={styles.container}>
      <HeaderTitle title="Font Size" navigation={navigation} />
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.section}>
            <CustomText style={styles.sectionTitle}>
              Choose Font Size
            </CustomText>
            <CustomText style={styles.sectionDescription}>
              Select your preferred font size. This will affect text throughout the app.
            </CustomText>
            
            <View style={styles.optionsContainer}>
              {fontSizeOptions.map((option) => (
                <TouchableOpacity
                  key={option}
                  style={[
                    styles.optionButton,
                    fontSizeScale === option && styles.selectedOption
                  ]}
                  onPress={() => onFontSizeChange(option)}
                >
                  <CustomText style={[
                    styles.optionText,
                    fontSizeScale === option && styles.selectedOptionText
                  ]}>
                    {FONT_SIZE_LABELS[option]}
                  </CustomText>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.section}>
            <CustomText style={styles.sectionTitle}>
              Preview
            </CustomText>
            <View style={styles.previewContainer}>
              <CustomText style={styles.previewTitle}>
                Sample Title Text
              </CustomText>
              <CustomText style={styles.previewBody}>
                This is how regular text will appear with your selected font size. 
                You can see how it affects readability and overall appearance of the app.
              </CustomText>
              <CustomText textType="bold" style={styles.previewBold}>
                This is bold text sample
              </CustomText>
              <CustomText textType="medium" style={styles.previewMedium}>
                This is medium weight text sample
              </CustomText>
            </View>
          </View>

          <View style={styles.section}>
            <CustomText style={styles.sectionTitle}>
              Current Setting
            </CustomText>
            <CustomText style={styles.currentSettingText}>
              {FONT_SIZE_LABELS[fontSizeScale]}
            </CustomText>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// Styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: rv(20),
  },
  section: {
    marginBottom: rv(30),
  },
  sectionTitle: {
    fontSize: rv(18),
    fontFamily: 'bold',
    color: theme.colors.text,
    marginBottom: rv(8),
  },
  sectionDescription: {
    fontSize: rv(14),
    fontFamily: 'regular',
    color: theme.colors.textSecondary,
    marginBottom: rv(20),
    lineHeight: rv(20),
  },
  optionsContainer: {
    gap: rv(12),
  },
  optionButton: {
    padding: rv(16),
    borderRadius: rv(12),
    borderWidth: 2,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
  },
  selectedOption: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary + '10',
  },
  optionText: {
    fontSize: rv(16),
    fontFamily: 'medium',
    color: theme.colors.text,
    textAlign: 'center',
  },
  selectedOptionText: {
    color: theme.colors.primary,
  },
  previewContainer: {
    padding: rv(20),
    borderRadius: rv(12),
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  previewTitle: {
    fontSize: rv(20),
    fontFamily: 'bold',
    color: theme.colors.text,
    marginBottom: rv(12),
  },
  previewBody: {
    fontSize: rv(14),
    fontFamily: 'regular',
    color: theme.colors.text,
    lineHeight: rv(20),
    marginBottom: rv(12),
  },
  previewBold: {
    fontSize: rv(14),
    marginBottom: rv(8),
  },
  previewMedium: {
    fontSize: rv(14),
  },
  currentSettingText: {
    fontSize: rv(16),
    fontFamily: 'medium',
    color: theme.colors.primary,
  },
}));

export default FontSizeSettingsContainer;
